{"expenses": "Expense Management", "login": "<PERSON><PERSON>", "email_address": "Email Address", "password": "Password", "remember_me": "Remember Me", "forgot_password": "Forgot Password", "login_success": "Login Successful", "invalid_credentials": "Invalid Credentials", "logout_success": "Log Out Successful", "unauthorised_user": "Unauthorised User", "welcome": "Welcome", "log_out": "Log Out", "profile": "Profile", "administration": "Administration", "employee-info": "Employee Info", "financeinfo": "Finance Info", "Form16Info": "Form 16", "hrControl": "HR Control", "Form16": "My Form 16", "reports": "Reports", "type_to_search": "Type to search", "total": "Total", "home": "Home", "action": "Action", "download": "Download", "upload": "Upload", "exports": "Export", "currency": "<PERSON><PERSON><PERSON><PERSON>", "admin": "Admin", "accounts": "Accounts", "add": "Add", "cancel": "Cancel", "currency_id": "Currency ID", "currency_name": "Currency Name", "currency_symbol": "Currency Symbol", "currency_code": "Currency Code", "sort_by_name": "Sort: Name", "edit": "Edit", "copy": "Copy", "delete": "Delete", "inactive": "Inactive", "prev-financial-statement": "Prev Financial Statement", "background-info": "Background Info", "professionalinfo": "Professional Info", "historyform": "History Form", "createform": "Create Form", "forms": "Forms", "backgroundinfo": "Background Info", "employeeportal": "My Portal", "employees": "Employees", "expected-joiners": "Expected Joiners", "product-analytics": "Analytics", "rca": "RCA", "workinfo": "Work Info", "projectmanagement": "Project/Resource Management", "organizationalchart": "Organizational Chart", "myTeam": "My Team", "srTab": "Service Request", "assetsmanagement": "Assets", "PaySlips": "Pay Slips", "InvestmentForm": "Investment Form", "BankInfo": "Bank Info", "employeelist": "Employee List", "drs": "DRs", "drslist": "DRs List", "company_name": "Company Name", "range": "Range", "financial_year": "Financial Year", "get": "Get", "unmapped_user_gl_code": "Unmapped User GL Code", "mapped_user_gl_code": "Mapped User GL Code", "select_sheet": "Select Sheet", "add_financial_cycle": "Add Financial Cycle", "financialCycleName": "Financial Cycle Name", "lock": "Lock", "add_organization": "Add Organiozation", "heirarchy": "Hierarchy", "entity_type": "Entity Type", "organization_type": "Organization Type", "select_entity": "Select Entity", "percentage_holdings": "Percentage Holding", "yes": "Yes", "no": "No", "parent_entity": "Parent Entity", "child_entity": "Child Entity", "financial_period": "Financial Period", "add_financial_period": "Add Financial Period", "lets_start_by_setting_up_your_administration_data": "Let's start by setting up your Administration data", "setup": "Set Up", "add_gaap_error": "Add atleast one gaap", "add_financial_cycle_error": "Add financial cycle first", "add_financial_period_error": "Add financial period first", "add_currency_error": "Add atleast one currency", "update": "Update", "financial_cycle_name_error": "Please provide financial cycle name", "financial_period_name_error": "Please provide financial period name", "financial_cycle_date_error": "Please select start time of financial cycle", "financial_period_date_error": "Please select both from and to period date", "template": "Template", "select_financial_cycle": "Select financial Cycle", "select_financial_cycle_error": "please select financial cycle", "effective_holdings": "Effcetive holdings", "holding": "Holding", "functional_currency": "Functional Currency", "financialPeriodName": "Financial Period Name", "accountingStandardName": "GAAP", "modules": "<PERSON><PERSON><PERSON>", "users": "Users", "assign_sub_modules": "Assign Submo<PERSON>les", "please_select_atleast_one_module_from_above": "Please select atleast one module from above.", "users_management": "User Management", "user_manage_message": "Manage your Uniqus User and with assigned roles.", "add_user_button": "Add User", "id": "ID", "user_name": "User Name", "designation": "Designation", "module_count": "Module Count", "user_management": "User Management", "add_new_user": "Add New User", "name": "Name", "assign_modules": "Assign <PERSON>", "assign_new": "Assign New", "currencyId": "Currency ID", "currencyName": "Currency Name", "currencySymbol": "Currency Symbol", "currencyCode": "Currency Code", "save": "Save", "next": "Next", "submit": "Submit", "baseCurrencyCode": "Base Currency Code", "targetCurrencyCode": "Target Currency Code", "exchangeRate": "Exchange Rates", "inverseExchangeRate": "Inverse Exchange Rates", "financialYear": "Financial Year", "fromPeriod": "Form Period", "toPeriod": "To Period", "masters": "Masters", "company": "Company", "period-lock": "Period Lock", "client_list": "Client list", "submodules": "Submodules", "org_structure": "Org. structure", "holding_percentage": "Holding Percentage", "hierarchy": "Hierarchy", "bulk_upload": "Bulk Upload", "entity_name": "Entity Name", "add_organisation": "Add Organization", "basic_info": "Basic Info", "additional_info": "Additional Info", "entity_code": "Entity Code", "country_of_incorporation": "Country Of Incorporation", "financial_cycle": "Financial Cycle", "financial_currency": "Financial Currency", "financial_currency_valid": "Financial Currency Valid Form", "reporting_currency": "Reporting Currency", "foreign_business_unit": "Foreign Business Unit", "valid_form": "Valid Form", "period": "Period", "entity": "Entity", "category": "Category", "value": "Value", "upload_template": "Upload Orgtemplate", "drag_and_drop": "Drag and Drop", "browse_files": "Browse Files", "replace": "Replace", "period_lock": "Period Lock", "email_id": "Email <PERSON>d", "email": "Email <PERSON>d", "holdingEntity": "Holding Entity", "childEntity": "Child Entity", "effectiveHolding": "Effective Holding", "totalHolding": "Total Holding", "entityName": "Entity Name", "are_you_sure_you_want_to_delete": "Are you sure you want to delete", "firstName": "User Name", "fromDate": "From Date", "toDate": "To Date", "organisation_hierarchy": "Organiozation Hierarchy", "upload_trial_balance": "Upload Trial Balance", "column_mapping": "Column Mapping", "mapped_trail_balance": "Mapped Trail Balance", "create_chart_of_account": "Create Chart of Account", "add_adjustments": "Add adjustments (If any)", "stand_alone_report": "Stand Alone Report", "balance_sheet": "Balance Sheet", "profile_loss_flow": "Profile Loss Flow", "socie_report": "SOCIE Report", "dashboard": "Dashboard", "statusSummary": "Status", "benefits": "Benefits", "leaveinfo": "Leave Info", "payslips": "Pay Slips", "taxReport": "Tax Report", "basicinfo": "Personal Info", "compensation": "Compensation", "investmentform": "Investment Form", "bankinfo": "Bank Info", "loan": "Loan", "info": "Info", "zoomin": "Zoom In", "zoomout": "Zoom Out", "fitscreen": "Fit Screen", "mapped_trial_balance": "Mapped Trial Balance", "upload_tb": "Upload TB", "reporting_period": "Reporting Period", "chart_of_accounts": "Chart Of Accounts", "target_column": "Target Column", "source_column": "Source Column", "mapped_gl_code": "Mapped GL Code", "unmapped_gl_code": "Unmapped GL Code", "create_coa": "Create CoA", "add_row": "Add Row", "export_xlxs": "Export xlxs", "gl_code": "GL Code", "grouping": "Grouping", "sub_grouping": "Sub grouping", "gl_description": "GL Description", "fsli": "FSLI", "gl_nature": "GL Nature", "accountingStandard": "GAAP", "confirm_password": "Confirm Password", "no_submodule_message": "Please select atleast one module", "confrim_password_error_message": "Please enter confirm password.", "password_error_message": "Please enter password.", "name_error_message": "Please enter name.", "designation_error_message": "Please enter designation.", "email_error_message": "Please enter email.", "module_error_message": "Please select atleast one module.", "assign_roles": "Assign Roles", "valid_email_error_message": "Email is not valid", "browse": "Browse", "import_adjustments": "Import Adjustments", "adjustments_xlxs": "Adjustments xlxs", "unmapped_xlxs": "Unmapped xlxs", "please_fill_all_fields_error": "Please fill in all the fields in the form.", "password_not_match": "Confirm password not matched", "module_null_message": "Select atleast one module", "role_null_message": "Select atleast one role", "no_module_message": "Select atleast one client", "Hello,": "Hello,", "Mr. User": "Mr. User", "recruitment": "Recruitment", "applicants": "Applicants", "templates": "Templates", "charts": "Charts", "settings": "Settings", "summary": "Summary", "expense": "Expense Management"}