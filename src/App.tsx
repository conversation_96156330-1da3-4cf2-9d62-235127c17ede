import React, { useEffect } from 'react'
import { Route, Routes } from 'react-router-dom'
import Dashboard from './components/Dashboard/Dashboard'
import ProtectedRoute from './auth/ProtectedRoute'
import Login from './components/Pages/Login'
import { Box } from '@mui/material'
import 'react-toastify/dist/ReactToastify.css'
import '../src/style.css'
import ForgotPassword from './components/Pages/ForgotPassword'
import MasterSetUp from './components/Dashboard/MasterSetUp'
import BasicInfo from './components/Dashboard/BasicInfo'
import BasicInfoOnSearch from './components/Dashboard/BasicInfoOnSearch'
import HolidayList from './components/Dashboard/HolidayList'
import Info from './components/EmployeeInfo/Info'
import BackgroundInfo from './components/EmployeeInfo/BackgroundInfo'
import AssignedAsset from './components/Dashboard/AssignedAsset'
import TodaysHighlights from './components/Dashboard/TodaysHighlights'
import LeaveDetails from './components/Dashboard/LeaveDetails'
import PaySlips from './components/Finance/PaySlips'
import Compensation from './components/Finance/Compensation'
import InvestmentForm from './components/Finance/InvestmentForm'
import BankInfo from './components/Finance/BankInfo'
import Loan from './components/Finance/Loan'
import ServiceRequest from './components/Dashboard/ServiceRequest'
import ProjectDetails from './components/Dashboard/ProjectDetails'
import EmployeeList from './components/EmployeeInfo/EmployeeList'
import DRs from './components/EmployeeInfo/DRs'
import DRsList from './components/EmployeeInfo/DRsList'
import StatusSummary from './components/Status/StatusSummary'
import AttendanceList from './components/EmployeeInfo/AttendanceList'
import RCAList from './components/Rca/RcaList'
import IDSR from './components/IDSR/IDSR'
import AssignedSR from './components/Dashboard/AssignedSR'
import ManagerView from './components/EmployeeInfo/ManagerView'
import ProjectGraph from './components/ProjectManagement/ProjectGraph'
import ProjectSheet from './components/ProjectManagement/ProjectSheet/Index'
import ProjectResourceReport from './components/ProjectManagement/ProjectResourceReport/ResourceReport'
import ProjectReports from './components/ProjectManagement/ProjectSheet/ProjectSheet'
import { useSelector } from 'react-redux'
import ProjectManagementReport from './components/ProjectManagement/ProjectManageReport/index'
import ProjectQAReports from './components/ProjectManagement/ProjectQAReport/index'
import OrgCharts from './components/OrgCharts'
import UserDetails from './components/UserDetails/UserDetails'
import NewUser from './components/UserDetails/NewUser'
import Reports from './components/Report'
import Reimbursement from './components/Finance/Reimebursement'
import Payroll from './components/Finance/Payroll'
import Form16Info from './components/Finance/Form16Info'
import Form16 from './components/Finance/Form16'
import EmpoloyeementInfo from './components/UserDetails/EmpoloyeementInfo'
import ExpectedJoiners from './components/ExpectedJoiners/ExpectedJoiners'
import NewJoiners from './components/ExpectedJoiners/NewJoiners'
import EmployeementInfo from './components/ExpectedJoiners/EmployeementInfo'
import ProjectAnalytics from './components/ProjectAnalytics'
import TaxReport from './components/Finance/TaxReport'
import BankInformation from './components/UserDetails/BankInformation'
import EmployeeTiles from './components/UserDetails/EmployeeTiles'
import AddBackgroundInfo from './components/EmployeeInfo/AddBackgroundInfo'
import SalarySlip from './components/UserDetails/SalarySlip'
import AdminCompensation from './components/UserDetails/AdminCompensation'
import AdminLeaveDetails from './components/UserDetails/AdminLeaveDetails'
import AdminPayroll from './components/UserDetails/AdminPayroll'
import Designations from './components/HrControl/Designations/index'
import CommonTiles from './components/CommonTilesPage/CommonTiles'
import AdminHolidayList from './components/HolidayList/AdminHolidayList'
import AdminTimingControl from './components/Timing/AdminTimingControl'
import QualificationSkills from './components/HrControl/QualificationSkills'
import AddLeaves from './components/HrControl/AddLeaves/AddLeaves'
import assetsInfo from './components/AssetsManagement/Assets'
import Assets from './components/AssetsManagement/Assets'
import AssetsList from './components/AssetsManagement/AssetsList'
import LeaveTypes from './components/HrControl/LeaveTypes'
import AssignedAssetReports from './components/AssetsManagement/ReportsPage/AssignedAssetReports'
import LeaveReports from './components/Reports/LeaveReports'
import Summary from './components/Summary/Summary'
import WorkHistory from './components/Finance/WorkHistory'
import Expense from './components/Expense'
import Candidates from './components/Recruitment/Candidates'
import Templates from './components/Recruitment/Templates'
import Contacts from './components/Recruitment/Settings/Users/<USER>'
import TemplateForm from './components/Recruitment/Templates/TemplateForm'
import QualificationScreen from './components/Recruitment/Settings/Candidate/QualificationScreen/QualificationScreen'
import BatchScreen from './components/Recruitment/Settings/Campus/BatchScreen/BatchScreen'
import Setting from './components/Recruitment/Settings/Setting'
import AddIgnoredBodyModal from './components/Recruitment/Settings/System/BlockedBody/AddIgnoredModal'
import Joiners from './components/Recruitment/Settings/Users/<USER>/Joiners'
import RequestPage from './components/Recruitment/Settings/Recruitment/FeedbackTemplate/RequestPage'
import InstitutePage from './components/Recruitment/Settings/Users/<USER>/Institute/InstitutePage'
import AddInstitutePage from './components/Recruitment/Settings/Users/<USER>/Institute/AddInstitutePage'
import CandidatePositionPage from './components/Recruitment/Settings/Candidate/Position/CandidatePositionPage'
import AddPositionPage from './components/Recruitment/Settings/Candidate/Position/AddPositionPage'
import Rounds from 'components/Recruitment/Settings/Rounds/Rounds'
import ConsultancyTable from './components/Recruitment/Settings/Users/<USER>/Consultancies/ConsultancyTable'
import RecruitersTable from './components/Recruitment/Settings/Recruiter/RecruitersTable'
import OrganizationsTable from './components/Recruitment/Settings/Organisation/OrganisationTable'
import TagsTable from './components/Recruitment/Settings/Candidate/Tags/TagsTable'
import CandidateProfile from './components/Recruitment/Candidates/Feedback/CandidateProfile'
import BlockedDomain from './components/Recruitment/Settings/System/BlockedDomain/BlockedDomain'
import UploadCandidate from './components/Recruitment/Settings/Recruitment/UploadCandidate/UploadCandidate'
import ExperienceTable from './components/Recruitment/Settings/Candidate/ExperienceTable/ExperienceTable'
import BlockedSubject from './components/Recruitment/Settings/System/BlockedSubject/BlockedSubject'
import CustomizedScreen from './components/Recruitment/Settings/System/Customize/customizeScreen'
import { Applicants } from './components/Recruitment'
import Charts from './components/Recruitment/Charts'
import AddCandidate from './components/Recruitment/Candidates/AddCandidate/AddCandidate'
import EditCandidate from './components/Recruitment/Candidates/EditCandidate/EditCandidate'
import InterviewerScreen from './components/Recruitment/Settings/Users/<USER>/interviewerScreen'
import AddInterviewer from './components/Recruitment/Settings/Users/<USER>/addInterviewer'
import TPOTable from 'components/Recruitment/Settings/Users/<USER>/Tpo/TPOTable'
import ScheduleTable from 'components/Recruitment/Settings/Schedule/scheduleTable'
import VideoPreviewPage from 'components/Recruitment/Candidates/Dashboard/CandidateVideo'

import AttendanceReports from './components/Reports/AttendanceReports'

function App() {
  //This useEffect Is an event lister that is responsible for page reload when user clears local storage.
  useEffect(() => {
    const handleStorageChange = (event: any) => {
      if (event.key === null) {
        window.location.reload()
      }
    }
    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const latestRoles = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { roles: string[] } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.roles,
  )

  const drsInfo = useSelector(
    (state: { entities: { dashboard: { getUserDetails: { drsCount: { count: number } } } } }) =>
      state?.entities?.dashboard?.getUserDetails?.drsCount?.count,
  )

  const role = latestRoles ?? localStorage.getItem('roles')
  const drsCount = drsInfo ?? localStorage.getItem('drsCount')
  const isAssignable = localStorage.getItem('is_assignable')

  return (
    <Box height={'100%'} width={'100%'}>
      <Routes>
        {/* {/ <Route path='employees/payroll' element={<AdminPayroll />} /> /} */}
        <Route path='/' element={<Login />} />

        {/* <Route path='home/basic-info' element={<BasicInfo />} />
        <Route path='home/holiday-list' element={<HolidayList />} />
        <Route path='home/assigned-asset' element={<AssignedAsset />} />
        <Route path='home/leave-details' element={<LeaveDetails />} />
        <Route path='home/service-request' element={<ServiceRequest />} />
        <Route path='home/project-details' element={<ProjectDetails />} /> */}
        <Route path='/home' element={<ProtectedRoute component={<Dashboard />} />}>
          <Route path='basic-info' element={<BasicInfo />} />
          <Route path='basic-info-on-search' element={<BasicInfoOnSearch />} />
          <Route path='holiday-list' element={<HolidayList />} />
          <Route path='assigned-asset' element={<AssignedAsset />} />
          <Route path='leave-details' element={<LeaveDetails />} />
          <Route path='service-request' element={<ServiceRequest />} />
          {isAssignable === 'true' && <Route path='assigned-request' element={<AssignedSR />} />}
          <Route path='project-details' element={<ProjectDetails />} />
          <Route path='todays-highlights' element={<TodaysHighlights />} />
          <Route path='drs' element={<DRs />} />
          <Route path='drs-list' element={<DRsList />} />
          <Route path='drs/leave-details' element={<LeaveDetails />} />
          <Route path='leaveinfo' element={<AttendanceList />} />
          <Route path='dashboard/basic-info' element={<BasicInfo />} />
          <Route path='dashboard/holiday-list' element={<HolidayList />} />
          <Route path='dashboard/assigned-asset' element={<AssignedAsset />} />
          <Route path='dashboard/leave-details' element={<LeaveDetails />} />
          <Route path='dashboard/service-request' element={<ServiceRequest />} />
          <Route path='dashboard/project-details' element={<ProjectDetails />} />
          <Route path='dashboard/todays-highlights' element={<TodaysHighlights />} />
          <Route path='dashboard/drs' element={<DRs />} />
          <Route path='dashboard/drs/manager-view' element={<ManagerView />} />
          <Route path='dashboard/drs-list' element={<DRsList />} />
          <Route path='dashboard/drs/leave-details' element={<LeaveDetails />} />
          <Route path='dashboard/attendance-list' element={<AttendanceList />} />
          <Route path='/home' element={<MasterSetUp />} />
          {(role?.includes('Admin') || role?.includes('Accountant')) && (
            <Route path='admin/Form16info' element={<Form16Info />} />
          )}
          {role?.includes('Admin') && <Route path='admin' element={<UserDetails />} />}
          {role?.includes('Admin') && <Route path='admin/employees' element={<UserDetails />} />}
          {role?.includes('Admin') && <Route path='admin/reports' element={<CommonTiles />} />}
          {role?.includes('Admin') && (
            <Route path='admin/reports/leaveReport' element={<LeaveReports />} />
          )}
          {role?.includes('Admin') && (
            <Route path='admin/reports/attendanceReport' element={<AttendanceReports />} />
          )}

          {role?.includes('Admin') && (
            <Route path='employees/employeeInfoTiles' element={<EmployeeTiles />} />
          )}
          {role?.includes('Admin') && (
            <Route path='employees/backgroundInfo' element={<AddBackgroundInfo />} />
          )}
          {role?.includes('Admin') && <Route path='employees/payroll' element={<AdminPayroll />} />}
          {role?.includes('Admin') && (
            <Route path='employees/LeaveDetails' element={<AdminLeaveDetails />} />
          )}
          {role?.includes('Admin') && (
            <Route path='employees/salarySlip' element={<SalarySlip />} />
          )}
          {role?.includes('Admin') && (
            <Route path='employees/compensation' element={<AdminCompensation />} />
          )}
          {role?.includes('Admin') && (
            <Route path='/home/<USER>/new-employees' element={<NewUser />} />
          )}
          {role?.includes('Admin') && (
            <Route path='/home/<USER>/new-joiners' element={<NewJoiners />} />
          )}
          {role?.includes('Admin') && (
            <Route path='admin/expected-joiners' element={<ExpectedJoiners />} />
          )}
          {role?.includes('Admin') && (
            <Route path='employees/employees-info' element={<EmpoloyeementInfo />} />
          )}
          {role?.includes('Admin') && (
            <Route path='employees/bank-info' element={<BankInformation />} />
          )}
          {role?.includes('Admin') && <Route path='employees/tax-report' element={<TaxReport />} />}
          {role?.includes('Admin') && (
            <Route path='employees/work-history' element={<WorkHistory />} />
          )}
          {role?.includes('Admin') && (
            <Route
              path='admin/expected-joiners/expected-joiners-info'
              element={<EmployeementInfo />}
            />
          )}
          {role?.includes('Asset Admin') && (
            <>
              <Route path='assetsmanagement' element={<Assets />} />
              <Route path='assetsmanagement/assetslist' element={<AssetsList />} />
              <Route
                path='assetsmanagement/assignedassetsreports'
                element={<AssignedAssetReports />}
              />
            </>
          )}
          <Route path='srTab' element={<ServiceRequest />} />
          <Route path='dashboard' element={<MasterSetUp />} />
          <Route path='employeeportal' element={<BasicInfo />} />
          <Route path='employeeportal/professionalinfo' element={<Info />} />
          <Route path='employeeportal/backgroundinfo' element={<BackgroundInfo />} />
          {/* {/ <Route path='employeeportal/rca' element={<RCAList />} /> /} */}
          <Route path='employeeportal/idsr' element={<IDSR />} />
          <Route path='employeeportal/basicinfo' element={<BasicInfo />} />
          <Route path='employeelist' element={<EmployeeList />} />
          <Route path='financeinfo' element={<BankInfo />} />
          <Route path='financeinfo/PaySlips' element={<PaySlips />} />
          <Route path='financeinfo/taxReport' element={<TaxReport />} />
          <Route path='financeinfo/Compensation' element={<Compensation />} />
          <Route path='financeinfo/InvestmentForm' element={<InvestmentForm />} />
          <Route path='financeinfo/BankInfo' element={<BankInfo />} />
          <Route path='financeinfo/Loan' element={<Loan />} />
          <Route path='financeinfo/Form16' element={<Form16 />} />
          <Route path='statusSummary' element={<StatusSummary />} />
          <Route path='workinfo' element={<IDSR />} />
          <Route path='summary' element={<Summary />} />
          <Route path='organizationalchart' element={<OrgCharts />} />
          {(role?.includes('Admin') || drsCount > 0) && (
            <Route path='myTeam' element={<Reports />} />
          )}
          {role?.includes('Admin') && (
            <Route path='admin/product-analytics' element={<ProjectAnalytics />} />
          )}
          {role?.includes('Admin') && <Route path='hrControl' element={<CommonTiles />} />}
          {role?.includes('Admin') && (
            <Route path='hrControl/designations' element={<Designations />} />
          )}
          {role?.includes('Admin') && (
            <Route path='hrControl/holidaysList' element={<AdminHolidayList />} />
          )}
          {role?.includes('Admin') && (
            <Route path='hrControl/officeArrivalTiming' element={<AdminTimingControl />} />
          )}
          {role?.includes('Admin') && (
            <Route path='hrControl/qualificationSkillSets' element={<QualificationSkills />} />
          )}
          {role?.includes('Admin') && <Route path='hrControl/addLeaves' element={<AddLeaves />} />}
          {role?.includes('Admin') && <Route path='hrControl/leaveType' element={<LeaveTypes />} />}
          {/* {/ {role?.includes('Admin') && <Route path='reports' element={<CommonTiles />} />} /} */}
          {role?.includes('Admin') && (
            <Route path='reports/leaveReports' element={<CommonTiles />} />
          )}
          {role?.includes('Admin') && (
            <Route path='reports/leaveReports/leaveReport' element={<LeaveReports />} />
          )}
          {role?.includes('Admin') && (
            <Route path='reports/leaveReport' element={<LeaveReports />} />
          )}
          {role?.includes('Project Manager') && (
            <Route path='projectmanagement' element={<ProjectGraph />} />
          )}

          {/* {/ <Route path='projectmanagement/Projects' element={<Projects />} /> / */}
          <Route path='projectmanagement/Projectgraphs' element={<ProjectGraph />} />
          <Route path='projectmanagement/projectreport' element={<ProjectSheet />} />
          <Route
            path='projectmanagement/ProjectResourceReport'
            element={<ProjectResourceReport />}
          />
          <Route path='projectmanagement/ProjectReports' element={<ProjectReports />} />
          <Route
            path='projectmanagement/ProjectManagementReport'
            element={<ProjectManagementReport />}
          />
          <Route path='projectmanagement/ProjectQAReports' element={<ProjectQAReports />} />
          <Route path='expense' element={<Expense />} />
          {/* {/ <Route path='*' element={<Navigate to='/home' />} /> /} */}
          {/* {/ Expense Management /} */}
          <Route path='expenses' element={<Expense />} />
        </Route>
        <Route
          path='/home/<USER>'
          element={
            <ProtectedRoute
              allowedRoles={['Recruiter', 'Master Admin']}
              component={<Dashboard />}
            />
          }
        >
          <Route path='recruitment/settings/rounds' element={<Rounds />} />
          <Route path='recruitment/tpo' element={<TPOTable />} />
          <Route path='recruitment/consultancy' element={<ConsultancyTable />} />
          <Route path='recruitment/settings/contacts' element={<Contacts />} />
          <Route path='recruitment/settings/users/contacts' element={<Contacts />} />
          <Route
            path='recruitment/settings/users/interviewer/addinterviewer'
            element={<AddInterviewer />}
          />
          <Route index element={<Candidates />} />
          <Route path='templates' element={<Templates />} />
          <Route path='templates/template/' element={<TemplateForm />} />
          <Route path='applicants' element={<Applicants />} />
          <Route path='charts' element={<Charts />} />
          <Route path='settings' element={<Setting />} />
          <Route path='settings/contacts' element={<Contacts />} />
          <Route path='settings/schedule' element={<ScheduleTable />} />
          <Route path='settings/recruiter' element={<RecruitersTable />} />
          <Route path='settings/organization' element={<OrganizationsTable />} />
          <Route path='settings/candidate/qualification' element={<QualificationScreen />} />
          <Route path='settings/campus/batch' element={<BatchScreen />} />
          <Route path='settings/campus/batch' element={<BatchScreen />} />
          <Route path='settings' element={<Setting />} />
          <Route path='settings/users/joiners' element={<Joiners />} />
          <Route path='settings/recruitment/feedback-template' element={<RequestPage />} />

          <Route path='candidates/addcandidate' element={<AddCandidate />} />
          <Route path='candidates/editcandidate/:id' element={<EditCandidate />} />

          <Route path='settings/users/contacts/institute' element={<InstitutePage />} />
          <Route
            path='settings/users/contacts/institute/add-institute'
            element={<AddInstitutePage />}
          />
          <Route path='settings/users/contacts/institute/edit/:id' element={<AddInstitutePage />} />
          <Route path='settings/candidate/position' element={<CandidatePositionPage />} />
          <Route path='settings/candidate/position/add-position' element={<AddPositionPage />} />
          <Route path='settings/user/index' element={<Contacts />} />
          <Route path='settings/campus/batch' element={<BatchScreen />} />
          <Route path='settings/system/blockedbody' element={<AddIgnoredBodyModal />} />
          <Route path='settings/users/index' element={<Contacts />} />
          <Route path='settings/candidate/tags' element={<TagsTable />} />
          <Route path='candidate/feedback/:id' element={<CandidateProfile />} />

          <Route path='settings/candidate/tags' element={<TagsTable />} />

          <Route path='candidate/feedback/feedback-screen' element={<CandidateProfile />} />
          <Route path='candidate/feedback/:id' element={<CandidateProfile />} />
          <Route path='settings/system/blocked-domain-table' element={<BlockedDomain />} />
          <Route path='settings/system/customize/' element={<CustomizedScreen />} />
          <Route path='settings/users/interviewer/' element={<InterviewerScreen />} />
          <Route path='settings/recruitment/upload-candidate' element={<UploadCandidate />} />
          <Route path='settings/candidate/experience-table' element={<ExperienceTable />} />
          <Route path='settings/system/blocked-subject' element={<BlockedSubject />} />
          <Route path='candidate/video-preview' element={<VideoPreviewPage />} />
        </Route>
        {/* <Route path='*' element={<Navigate to='/home' />} /> */}
        <Route path='/site/forgotPassword' element={<ForgotPassword />} />
      </Routes>
    </Box>
  )
}

export default App
