import { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { loaderProps } from '../Common/CommonStyles'
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  Grid,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  styled,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material'
import {
  CommonButton,
  InputField,
  SelectField,
  StyledMenuItem,
  useStyles,
} from './EmpoloyeementInfoStyle'
import { FormValues } from './IFormValue'
import { RootState } from '../../configureStore'
import {
  employeePortalEntity,
  fetchUserDetailsEntities,
  fetchUserDetailsUI,
  projectManagementEntity,
  SREntity,
} from '../../reducers'
import {
  createUser,
  fetchAllRoles,
  fetchAllStates,
  fetchAllCountries,
  fetchAllFloors,
  fetchAllWorkstation,
  fetchAllLocation,
  uploadUserImg,
  updateUser,
  fetchAllClientLocation,
  getUserImage,
  fetchDesignation,
  fetchDesignationBand,
  fetchWorkingEmp,
  fetchTiming,
  fetchEmployeeInfo,
  fetchServiceRequestDepartment,
} from '../../actions'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { toast } from 'react-toastify'
import { ArrowBack } from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { DemoContainer } from '@mui/x-date-pickers/internals/demo'
import styles from '../../utils/styles.json'
import styless from '../../utils/styles.json'
import { FormControl } from '@mui/material'
// import styless from '../../../utils/styles.json'
import { debugPort } from 'process'
import moment from 'moment'
import AlertPopUp from '../Common/AlertPopUp'
import WarningIcon from '@mui/icons-material/Warning'
import Loader from '../Common/Loader'
// import { serviceRequestDepartment } from '../../services'
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import DepartmentDropdown from './departmentDropDown'

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const style = {
  checkBoxItem: { fontSize: '14px', padding: '3px 12px' },
  selectStyle: {
    '& .MuiSelect-select': {
      height: '15px',
    },
  },
  gridStyle: {
    '& .MuiFormControl-root': {
      margin: '0 0 15px',
    },
  },
}

export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-input': {
    padding: '13.5px 14px', // Adjust the padding here
    fontSize: '14px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
    marginLeft: '-13px',
  },
  '& .MuiFormLabel-asterisk': {
    color: 'red',
  },
  '& .MuiInputBase-root.MuiOutlinedInput-root': {
    borderRadius: '20px',
    width: '92%',
    fontFamily: styles.FONT_MEDIUM,
    marginLeft: '15px',
  },
  '& .MuiFormLabel-root.MuiInputLabel-root': {
    marginLeft: '15px',
    marginTop: '2px',
    fontSize: '14px', // Adjust the font size here
    fontFamily: styles.FONT_MEDIUM,
    backgroundColor: 'white',
    padding: '0 7px',
  },
}))

const EmpoloyeementInfo = (props: any) => {
  const {
    isUserUpdating,
    updateUser,
    fetchEmployeementInfo,
    isEmployeementInfoUpdated,
    employeementInfoData,
    isGetEmpoyeementInfoLoader,
    restFetchEmployee,
    fetchDepartmentDataFetch,
    serviceRequestDepartment
  } = props

  const rowdata: any = useLocation()?.state
  const empInfo: any = employeementInfoData?.employmentInfo
  const getEmployeeInfo: any = employeementInfoData
  const [searchQuery, setSearchQuery] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [userDepartment, setUserDepartment] = useState<string[]>([])
  const [title, setTitle] = useState('')
  const [message, setMessage] = useState('')

  const [open, setOpen] = useState(false)
  const [hasPermission, setHasPermission] = useState(true);



  const onClose = () => {
    setOpen(false)
  }
  const onConfirm = (title: string) => {
    title === 'Confirm Poor Performance?' && formik.setFieldValue('poor_performance_review', '1')
    title === 'Confirm Maternity Leave?' && formik.setFieldValue('under_maternity', '1')

    formik.setFieldValue('salary_on_hold', '1')
    setOpen(false)
  }

  const handleMaternity = (value: string) => {
    setTitle('Confirm Maternity Leave?')
    setMessage(
      'By confirming, you are requesting maternity leave. Please note that the regular salary will be suspended during this period',
    )

    if (value === '1') {
      setOpen(true)
    } else {
      formik.setFieldValue('under_maternity', '0')
      setOpen(false)
    }
  }

  const handlePoorPerformance = (value: string) => {
    setTitle('Confirm Poor Performance?')
    setMessage(
      "Are you sure you want to continue? If you confirm, the employee's salary will automatically be set to 'On Hold' due to the 'Poor Performance' status",
    )
    if (value === '1') {
      setOpen(true)
    } else {
      formik.setFieldValue('poor_performance_review', '0')
      setOpen(false)
    }
  }

  const classes = useStyles()
  const navigate = useNavigate()
  const location = useLocation()
  // const userData = location?.state?.data?.rowData || {}

  const [searchParams] = useSearchParams()
  const userId = searchParams.get('userId') 

  useEffect(() => {
    props.fetchAllRole()
    props.fetchAllState()
    props.fetchAllCountry()
    props.fetchAllFloor()
    props.fetchAllWorkstation()
    props.fetchAllLocation()
    props.fetchAllClientLocation()

    if (rowdata?.userid || userId) {
      // props.getUserImage(rowdata)
      fetchEmployeementInfo({ userId: rowdata?.userid || userId})
    }

    return () => {
      props.resetCreateUser()
      props.resetUpdateUser()
      props.resetUserImage()
    }
  }, [rowdata?.userid])

  useEffect(() => {
    if (employeementInfoData?.employmentInfo) {
      const empInfo = employeementInfoData.employmentInfo
      const getEmployeeInfo = employeementInfoData
      if (props?.AllEmpList?.length) {
        formik.setValues({
          employee_id: getEmployeeInfo?.employee_id ?? '',
          under_probation: getEmployeeInfo?.under_probation === 0 ? 0 : 1,
          desig_band: empInfo?.designation_band ?? '',
          manager: empInfo?.id_manager ?? '',
          status: empInfo?.status ? Number(empInfo?.status) : 1,
          probation_period: Number(getEmployeeInfo?.probation_period) ?? 12,
          pf_applicable: empInfo?.pf_applicable ?? 1,
          pf_no: empInfo?.pf_no ?? '',
          uan_no: empInfo?.uan_no ?? '',
          pan_card_no: empInfo?.pan_card_no ?? '',
          emp_type: empInfo?.employment_type ? Number(empInfo?.employment_type) : 1,
          aadhaar_card_no: empInfo?.aadhaar_card_no ?? '',
          referred_by: { userId: 0, name: '' },
          service_agreement: empInfo?.id_service_agreement ?? '',
          service_agreement_amount: empInfo?.service_agreement_amount ?? '75000',
          experience: empInfo?.working_experience ?? '',
          isIDCard: empInfo?.id_card_issued ?? '0',
          under_maternity: empInfo?.under_maternity ?? '0',
          maternity_start_date: formattedMaternityStartDate,
          maternity_end_date: formattedMaternityEndDate,
          salary_on_hold: empInfo?.salary_on_hold ?? '0',
          poor_performance_review: empInfo?.under_performance_review ?? '0',
          isMediclaim: empInfo?.mediclaim_issued ?? '0',
          mediclaim_no: empInfo?.mediclaim_no ?? '',
          passport_no: empInfo?.passport_no ?? '',
          tshirt_size: empInfo?.tshirt_size ?? 0,
          under_notice: getEmployeeInfo?.under_notice ?? 0,
          notice_period: getEmployeeInfo?.notice_period ?? 0,
          day_to_visit: empInfo?.day_to_visit ?? '',
          relieving_type: empInfo?.termination_type ?? 0,
          comment_from_HR: empInfo?.description ?? '',
          final_settlement_done: empInfo?.final_settlement_done,
          last_day_of_working: formattedLastDateOfWorking ?? '',
          first_performance_review: empInfo?.first_performance_review ?? '0',
          first_weekend: empInfo?.first_weekend ?? 'Saturday',
          second_weekend: empInfo?.second_weekend ?? 'Sunday',
          hire_date: formattedHireDate ?? '',
          start_date: formattedEffectiveDate ?? '',
          tentative_termination_date: formattedNoticeEndDate ?? '',
          notice_date: formattedNoticeStartDate ?? '',
          from_date: formattedStartDate ?? '',
          to_date: formattedEndDate ?? '',
          empDesignation: empInfo?.id_grade ?? '',
          shift_timing: getEmployeeInfo?.officeLog?.timing_id ?? 47,
          departments: getEmployeeInfo?.user_department,
          notice_description: getEmployeeInfo?.notice_description,
        })

        formik.setFieldValue(
          'referred_by',
          props?.AllEmpList?.find(
            (val: { userId: number }) => val?.userId === empInfo?.referred_by,
          ) ?? { userId: 0, name: '' },
        )
      }
    }
  }, [employeementInfoData, props?.AllEmpList]) // Dependency on fetched data

  useEffect(() => {
    props.fetchDesignationData()
    props.fetchAllEmpAPI()
    props.fetchDesignationBandData()
    props.fetchTimingData()
    serviceRequestDepartment({ userId: rowdata?.userid || userId})
    // setUserDepartment(formik.values.departments)
  }, [])

  useEffect(() => {
    getData()
    console.log("fetchDepartmentDataFetch", fetchDepartmentDataFetch)
    setHasPermission(fetchDepartmentDataFetch?.permission);
  }, [fetchDepartmentDataFetch])

  const handleReset = () => {
    formik.resetForm()
    navigate(-1)
  }

  useEffect(() => {
    if (props.isUserUpdated) {
      toast.success('User updated successfully')
      handleReset()
    }
  }, [props.isUserUpdated])

  useEffect(() => {
    if (empInfo?.userid) {
      restFetchEmployee()
    }
  }, [empInfo])

  const start_date = moment().format('YYYY-MM-DD')
  const end_date = moment().add(30, 'days').format('YYYY-MM-DD')
  const end_notice_date = moment()
    .add((getEmployeeInfo?.notice_period || 0) * 30, 'days')
    .format('YYYY-MM-DD');


  const last_day_of_working = moment().format('YYYY-MM-DD')

  const formattedMaternityStartDate = empInfo?.maternity_start_date
    ? moment(empInfo?.maternity_start_date).format('YYYY-MM-DD')
    : moment().format('YYYY-MM-DD') // Default to today's date if not provided

  const formattedMaternityEndDate = empInfo?.maternity_end_date
    ? moment(empInfo?.maternity_end_date).format('YYYY-MM-DD')
    : moment(formattedMaternityStartDate).add(6, 'months').format('YYYY-MM-DD') // Add 6 months to start date

  const formattedStartDate = getEmployeeInfo?.poor_performance?.start_date
    ? moment(getEmployeeInfo?.poor_performance?.start_date).format('YYYY-MM-DD')
    : start_date
  const formattedNoticeStartDate = getEmployeeInfo?.notice_date
    ? moment(getEmployeeInfo?.notice_date).format('YYYY-MM-DD')
    : start_date
  const formattedNoticeEndDate = getEmployeeInfo?.tentative_termination_date
    ? moment(getEmployeeInfo?.tentative_termination_date).format('YYYY-MM-DD')
    : end_notice_date

  const formattedEndDate = getEmployeeInfo?.poor_performance?.end_date
    ? moment(getEmployeeInfo?.poor_performance?.end_date).format('YYYY-MM-DD')
    : end_date

  const formattedLastDateOfWorking = empInfo?.termination_Date
    ? moment(empInfo?.termination_Date).format('YYYY-MM-DD')
    : ''

  const formattedHireDate = empInfo?.hire_date ? dayjs(empInfo?.hire_date).format('YYYY-MM-DD') : ''

  const formattedEffectiveDate = getEmployeeInfo?.officeLog?.start_date
    ? dayjs(getEmployeeInfo?.officeLog?.start_date).format('YYYY-MM-DD')
    : ''

  const validationSchema = Yup.object({
    service_agreement: Yup.string().required('Required'),
    hire_date: Yup.string().required('Required'),
    first_weekend: Yup.string().required('Required'),
    second_weekend: Yup.string().required('Required'),
    start_date: Yup.string().required('Required'),
    maternity_start_date: Yup.string().when(
      'under_maternity',
      ([under_maternity]: any, schema: any) => {
        return under_maternity === '1' ? schema.required('Required') : schema.optional()
      },
    ),
    maternity_end_date: Yup.string().when(
      'under_maternity',
      ([under_maternity]: any, schema: any) => {
        return under_maternity === '1' ? schema.required('Required') : schema.optional()
      },
    ),
    last_day_of_working: Yup.string().when(
      'relieving_type',
      ([relieving_type]: any, schema: any) => {
        return Number(relieving_type) === 0 ? schema.optional() : schema.required('Required')
      },
    ),
  })

  const formik = useFormik({
    initialValues: {
      employee_id: getEmployeeInfo?.employee_id ?? '',
      under_probation: getEmployeeInfo?.under_probation === 0 ? 0 : 1,
      desig_band: empInfo?.designation_band ?? '',
      manager: empInfo?.id_manager ?? '',
      status: empInfo?.status ? Number(empInfo?.status) : 1,
      probation_period:
        getEmployeeInfo?.under_probation === 0 ? 0 : getEmployeeInfo?.probation_period ?? 12,
      pf_applicable: empInfo?.pf_applicable ?? 1,
      pf_no: empInfo?.pf_no ?? '',
      uan_no: empInfo?.uan_no ?? '',
      pan_card_no: employeementInfoData?.employmentInfo?.pan_card_no ?? '',
      emp_type: empInfo?.employment_type ? Number(empInfo?.employment_type) : 1,
      aadhaar_card_no: empInfo?.aadhaar_card_no ?? '',
      referred_by: { userId: 0, name: '' },
      service_agreement: empInfo?.id_service_agreement ?? '',
      service_agreement_amount: empInfo?.service_agreement_amount ?? '75000',
      experience: empInfo?.working_experience ?? '',
      isIDCard: empInfo?.id_card_issued ?? '0',
      under_maternity: empInfo?.under_maternity ?? '0',
      maternity_start_date: formattedMaternityStartDate,
      maternity_end_date: formattedMaternityEndDate,
      salary_on_hold: empInfo?.salary_on_hold ?? '0',
      poor_performance_review: empInfo?.under_performance_review ?? '0',
      isMediclaim: empInfo?.mediclaim_issued ?? '0',
      mediclaim_no: empInfo?.mediclaim_no ?? '',
      passport_no: empInfo?.passport_no ?? '',
      tshirt_size: empInfo?.tshirt_size ?? 0,
      under_notice: getEmployeeInfo?.under_notice ?? 0,
      notice_period: getEmployeeInfo?.notice_period ?? 0,
      day_to_visit: empInfo?.day_to_visit ?? '',
      last_day_of_working: formattedLastDateOfWorking ?? '',
      relieving_type: empInfo?.termination_type ?? 0,
      final_settlement_done: empInfo?.final_settlement_done ?? 0,
      comment_from_HR: empInfo?.description ?? '',
      first_performance_review: empInfo?.first_performance_review ?? '0',
      first_weekend: empInfo?.first_weekend ?? 'Saturday',
      second_weekend: empInfo?.second_weekend ?? 'Sunday',
      hire_date: formattedHireDate ?? '',
      start_date: formattedEffectiveDate ?? '',
      tentative_termination_date: formattedNoticeEndDate ?? '',
      notice_date: formattedNoticeStartDate ?? '',
      from_date: formattedStartDate ?? '',
      to_date: formattedEndDate ?? '',

      empDesignation: empInfo?.id_grade ?? '',
      shift_timing: getEmployeeInfo?.officeLog?.timing_id ?? 47,
      departments: getEmployeeInfo?.user_department,
      notice_description: getEmployeeInfo?.notice_description,
    },
    validationSchema,
    onSubmit: () => { },
  })

  useEffect(() => {
    if (formik.values.under_notice === 1) {
      const today = dayjs()
      formik.setFieldValue('notice_start_date', today.format('YYYY-MM-DD'))

      const period = Number(formik.values.notice_period)
      if (period) {
        const endDate = today.add(period, 'month')
        formik.setFieldValue('notice_end_date', endDate.format('YYYY-MM-DD'))
      } else {
        formik.setFieldValue('notice_end_date', '')
      }
    } else {
      formik.setFieldValue('notice_start_date', '')
      formik.setFieldValue('notice_end_date', '')
    }
  }, [formik.values.under_notice, formik.values.notice_period])

  const handleSubmit = async () => {
    try {
      await validationSchema.validate(formik.values, { abortEarly: false })

      const noticeDate = new Date(formik.values.notice_date);
      const tentativeTerminationDate = new Date(formik.values.tentative_termination_date);

      if (
        formik.values.tentative_termination_date &&
        tentativeTerminationDate < noticeDate
      ) {
        formik.setFieldError(
          'tentative_termination_date',
          'Tentative termination date must be after or equal to notice date'
        );
        // formik.setFieldTouched('tentative_termination_date', true, true);
        return; // Stop further processing
      }
      const entity = {
        userId: rowdata?.userid,
        employee_id: String(formik.values.employee_id),
        under_probation: Number(formik.values.under_probation),
        status: Number(formik.values.status),
        probation_period: Number(formik.values.probation_period),
        id_manager: formik.values.manager, //manger
        id_grade: formik.values?.empDesignation, //designation
        designation_band: formik.values.desig_band, //designation
        hire_date: formik.values.hire_date,
        under_notice: Number(formik.values.under_notice),
        notice_period: Number(formik.values.notice_period),
        referred_by: formik.values.referred_by?.userId,
        employment_type: formik.values.emp_type,
        pan_card_no: formik.values.pan_card_no,
        aadhaar_card_no: formik.values.aadhaar_card_no,
        id_service_agreement: formik.values.service_agreement,
        service_agreement_amount: formik.values.service_agreement_amount,
        mediclaim_issued: formik.values.isMediclaim,
        mediclaim_no: formik.values.mediclaim_no,

        termination_type: formik.values.relieving_type,
        ...(Number(formik.values.relieving_type) > 0 && {
          termination_Date: formik.values.last_day_of_working,
          comment_from_HR: formik.values.comment_from_HR,
        }),
        notice_start_date: formik.values.notice_date,
        notice_end_date: formik.values.tentative_termination_date,
        final_settlement_done: Number(formik.values.final_settlement_done),
        pf_applicable: formik.values.pf_applicable,
        pf_no: formik.values.pf_no,
        uan_no: formik.values.uan_no,
        passport_no: formik.values.passport_no,
        working_experience: formik.values.experience,
        under_performance_review: formik.values.poor_performance_review,
        under_maternity: formik.values.under_maternity,
        ...(Number(formik.values.under_maternity) !== 0 && {
          maternity_start_date: formik.values.maternity_start_date,
          maternity_end_date: formik.values.maternity_end_date,
        }),
        id_card_issued: formik.values.isIDCard,
        salary_on_hold: formik.values.salary_on_hold,
        first_performance_review: formik.values.first_performance_review,
        first_weekend: formik.values.first_weekend,
        second_weekend: formik.values.second_weekend,
        tshirt_size: formik.values.tshirt_size,
        updateEmpInfoFlag: 1,
        start_date: formik.values.start_date,
        ...(Number(formik.values.poor_performance_review) !== 0 && {
          poor_performance_start_date: formik.values.from_date,
          poor_performance_end_date: formik.values.to_date,
        }),
        timing_id: Number(formik.values.shift_timing),
        update: 1,
        notice_description: formik.values.notice_description,
        department_user: userDepartment.join(',')
      }
      updateUser(entity)
    } catch (error: any) {
      if (error instanceof Yup.ValidationError) {
        const stepErrors = error.inner.reduce((acc: any, curr: any) => {
          if (curr.path) {
            acc[curr.path as keyof FormValues] = curr.message
          }
          return acc
        }, {} as { [K in keyof FormValues]?: string })

        formik.setTouched(
          Object.keys(stepErrors).reduce((acc, key) => {
            acc[key as keyof FormValues] = true
            return acc
          }, {} as { [K in keyof FormValues]: any }),
        )

        formik.setErrors(stepErrors)
      } else {
        console.error('Unexpected error:', error)
      }
    }
  }

  const CustomFormControll = styled(FormControl)(() => ({
    marginLeft: '15px',
    marginTop: '15px',
  }))

  const daysOfWeek = [
    { id: 1, value: 'Sunday' },
    { id: 2, value: 'Monday' },
    { id: 3, value: 'Tuesday' },
    { id: 4, value: 'Wednesday' },
    { id: 5, value: 'Thursday' },
    { id: 6, value: 'Friday' },
    { id: 7, value: 'Saturday' },
  ]

  const getData = (() => {
    let departments = fetchDepartmentDataFetch?.department
      ?.filter((dept: any) => dept.checked)
      .map((dept: any) => dept.id)
    setUserDepartment(departments || [])
    return departments || []
  })

  const ITEM_HEIGHT = 48
  const ITEM_PADDING_TOP = 8
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
    // autoFocus: false,
    // disableAutoFocus: true,
    // disableAutoFocusItem: true,
    // // Keep the menu open
    // onClose: (event: any) => {
    //   event.stopPropagation();
    // },
    MenuListProps: {
      // 👇 This is the key part: stop the menu from closing on click
      onMouseDown: (event: React.MouseEvent) => {
        event.stopPropagation();
      },
    },
  }

  const StyledSelectField = styled(Select)(({ theme }) => ({
    width: "92%",
    borderRadius: '20px',
    '& .MuiSelect-select': {
      padding: '9px 11px',
      fontSize: '13px',
      fontFamily: styless.FONT_MEDIUM,
      borderRadius: '20px',
    },
    '&.MuiInputBase-root.MuiOutlinedInput-root.MuiSelect-root': {
      borderRadius: '20px',
      padding: '9px 11px',
      marginLeft: '-1px',
    },
  }))

  const handleAutocompleteChange = (event: any, newValue: any) => {
    formik.setFieldValue('referred_by', newValue || '')
  }

  const handleStartDateChange = (date: any) => {
    formik.setFieldValue('notice_date', date?.format('YYYY-MM-DD'))
    const endDate = date.add(+formik.values.notice_period * 30, 'day')
    formik.setFieldValue('tentative_termination_date', endDate?.format('YYYY-MM-DD'))
  }
 
  const handleUserDepartmentChange = (event: any) => {
    const {
      target: { value },
    } = event
    // setUserDepartment(value)
    setUserDepartment(typeof value === 'string' ? value.split(',') : value);

  }
  const [open1, setOpen1] = useState(false);
  console.log('Rendering Select dropdown');

  return (
    <div className={classes.root}>
      {isUserUpdating ||
        (isGetEmpoyeementInfoLoader && (
          <Loader state={(isUserUpdating || isGetEmpoyeementInfoLoader)} />
        ))}

      <AlertPopUp
        open={open}
        setOpen={setOpen}
        onClose={onClose}
        onConfirm={onConfirm}
        title={title}
        message={message}
      />
      <form onSubmit={formik.handleSubmit}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center', // Center items vertically
            justifyContent: 'space-between', // Space items apart (button left, heading center)
            width: '90%', // Set width as per your requirement
            height: '70px', // Set height as per your requirement
            borderRadius: '8px', // Round corners of the box
            marginLeft: '35px', // Center the box horizontally
            padding: '0 20px', // Optional: Add horizontal padding
            opacity: 1, // Adjust opacity as needed
            backgroundColor: 'white',
            marginTop: '20px',
          }}
        >
          {/* Centered heading */}
          <Typography
            sx={{
              fontFamily: styles.FONT_BOLD,
              fontSize: '32px', // Set font size
              fontWeight: 700, // Set font weight
              lineHeight: '38.73px', // Set line height
              textAlign: 'center', // Center align the text
              flexGrow: 1, // Allow the heading to grow and take available space
              margin: '0', // Optional: Remove default margin
              color: '#193C6C',
            }}
          >
            Update {getEmployeeInfo?.first_name ? `${getEmployeeInfo?.first_name}'s` : ''}{' '}
            Employment Info
          </Typography>
          <Box onClick={() => navigate(-1)} sx={{ float: 'right', mt: '0px', cursor: 'pointer' }}>
            <ArrowBack />
          </Box>
        </Box>
        <Box sx={{ mx: 4, my: 1, mt: 3, backgroundColor: '' }}>
          <div>
            <Box sx={{ display: 'flex', gap: '20px' }}>
              {/* First Section */}
              <Box width='66%'>
                <Grid container sx={{ display: 'flex', gap: '20px' }}>
                  <Grid
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '20px',
                      width: '100%',
                      height: 'fit-content',
                    }}
                  >
                    <Grid item xs={12} sx={{ backgroundColor: 'white', borderRadius: '8px' }}>
                      <Box
                        sx={{
                          borderBottom: '1px solid #193C6C', // Move border to the container
                          width: '100% !important', // Ensure the container takes up the full width
                        }}
                      >
                        <Typography
                          padding={'10px 10px'} // Retain padding inside the Typography
                          sx={{
                            fontFamily: styles.FONT_BOLD,
                            fontSize: '16px', // Use quotes around the size
                            fontWeight: 700, // Use camelCase for font-weight
                            lineHeight: '19.36px', // Use quotes around the line height
                            textAlign: 'left', // Use camelCase for text-align
                            color: '#193C6C',
                          }}
                        >
                          Finance Info
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Grid item xs={5.7}>
                          <Grid item>
                            <InputField
                              size='small'
                              fullWidth
                              id='pan_card_no'
                              name='pan_card_no'
                              label='Pan Number'
                              required={false}
                              value={formik.values.pan_card_no}
                              onChange={formik.handleChange}
                            />
                          </Grid>
                          <Grid item>
                            <SelectField
                              size='small'
                              id='pf_applicable'
                              name='pf_applicable'
                              label='Pf Applicable'
                              select
                              value={formik.values.pf_applicable}
                              onChange={formik.handleChange}
                              sx={style.selectStyle}
                              required={false}
                            >
                              <StyledMenuItem value={1}> Yes </StyledMenuItem>
                              <StyledMenuItem value={0}> No </StyledMenuItem>
                            </SelectField>
                          </Grid>
                          <Grid item>
                            <InputField
                              size='small'
                              fullWidth
                              id='pf_no'
                              name='pf_no'
                              label='PF Number'
                              value={formik.values.pf_no}
                              required={false}
                              onChange={formik.handleChange}
                              disabled={formik.values.pf_applicable === 1 ? false : true}
                            />
                          </Grid>
                        </Grid>
                        <Grid item xs={5.7}>
                          <Grid item>
                            <Box
                              display='flex'
                              justifyContent='center'
                              alignItems='center'
                              width='100%' // Adjust height as needed
                            >
                              <InputField
                                size='small'
                                fullWidth
                                id='aadhaar_card_no'
                                name='aadhaar_card_no'
                                label='Aadhaar Number'
                                onChange={(e: any) => {
                                  let onlyNums = e.target.value.replace(/[^0-9]/g, '')
                                  if (onlyNums.length > 12) {
                                    onlyNums = onlyNums.slice(0, 12)
                                  }
                                  formik.setFieldValue('aadhaar_card_no', onlyNums)
                                }}
                                value={formik.values.aadhaar_card_no}
                                required={false}
                              />
                            </Box>
                          </Grid>
                          <Grid item>
                            <InputField
                              size='small'
                              required={false}
                              fullWidth
                              id='uan_no'
                              name='uan_no'
                              label='UAN Number'
                              value={formik.values.uan_no}
                              onChange={formik.handleChange}
                              disabled={formik.values.pf_applicable === 1 ? false : true}
                            />
                          </Grid>

                          <Grid item sx={{ marginBottom: '20px' }}>
                            <SelectField
                              size='small'
                              id='salary_on_hold'
                              name='salary_on_hold'
                              label='Salary On Hold'
                              select
                              value={formik.values.salary_on_hold}
                              onChange={formik.handleChange}
                              sx={style.selectStyle}
                              required={false}
                            >
                              {' '}
                              <StyledMenuItem value={'1'}>Yes</StyledMenuItem>
                              <StyledMenuItem value={'0'}>No</StyledMenuItem>
                            </SelectField>
                          </Grid>
                        </Grid>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sx={{
                      flex: 1,
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      paddingBottom: '15px',
                    }}
                  >
                    <Typography
                      padding={'10px 10px'} // Retain padding inside the Typography
                      sx={{
                        fontFamily: styles.FONT_BOLD,
                        fontSize: '16px', // Use quotes around the size
                        fontWeight: 700, // Use camelCase for font-weight
                        lineHeight: '19.36px', // Use quotes around the line height
                        textAlign: 'left', // Use camelCase for text-align
                        borderBottom: '1px solid #193C6C',
                        color: '#193C6C',
                      }}
                    >
                      Service Info
                    </Typography>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Grid item xs={5.7}>
                        <Grid item>
                          <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DemoContainer
                              components={['DatePicker']}
                              sx={{ width: '100%', marginTop: '-5px' }}
                            >
                              <Box sx={{ width: '92%' }}>
                                <DatePicker
                                  sx={{
                                    border: 'none',
                                    '& .MuiOutlinedInput-root': {
                                      marginLeft: '15px',
                                      borderRadius: '20px',
                                      height: '40px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                    },
                                    '& .MuiInputLabel-root': {
                                      marginLeft: '15px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                      marginTop: '-5px',
                                      '& .MuiFormLabel-asterisk': {
                                        color: 'red',
                                      },
                                    },
                                  }}
                                  label='Hire Date'
                                  value={
                                    formik.values.hire_date ? dayjs(formik.values.hire_date) : null
                                  }
                                  onChange={(date: any) => {
                                    formik.setFieldValue('hire_date', date?.format('YYYY-MM-DD'))
                                  }}
                                  slotProps={{
                                    textField: {
                                      error:
                                        formik.touched.hire_date &&
                                        Boolean(formik.errors.hire_date),
                                    },
                                  }}
                                />
                              </Box>
                            </DemoContainer>
                          </LocalizationProvider>
                        </Grid>
                        <SelectField
                          size='small'
                          id='experience'
                          name='experience'
                          label='Experience while joinning'
                          select
                          value={formik.values.experience}
                          onChange={formik.handleChange}
                          sx={{ ...style.selectStyle, marginTop: '0' }}
                          required={false}
                        >
                          <StyledMenuItem value={'0'}> 0 - 0.5 Year </StyledMenuItem>
                          <StyledMenuItem value={'1'}> 0.5 - 1 Year </StyledMenuItem>
                          <StyledMenuItem value={'2'}> 1 - 2 Year </StyledMenuItem>
                          <StyledMenuItem value={'3'}> 2 - 5 Year </StyledMenuItem>
                          <StyledMenuItem value={'4'}> 5 - 10 Year </StyledMenuItem>
                          <StyledMenuItem value={'5'}> 10+ Year </StyledMenuItem>
                        </SelectField>
                        <SelectField
                          size='small'
                          id='service_agreement'
                          name='service_agreement'
                          label='Service Agreement Period'
                          select
                          value={formik.values.service_agreement}
                          error={
                            formik.touched.service_agreement &&
                            Boolean(formik.errors.service_agreement)
                          }
                          required={true}
                          onChange={formik.handleChange}
                          sx={style.selectStyle}
                        >
                          <StyledMenuItem value={0}> NA </StyledMenuItem>
                          <StyledMenuItem value={3}> 1 year </StyledMenuItem>
                          <StyledMenuItem value={2}> 1.5 year </StyledMenuItem>
                          <StyledMenuItem value={1}> 2 year </StyledMenuItem>
                        </SelectField>

                        <SelectField
                          select
                          size='small'
                          id='under_probation'
                          label='Under Probation'
                          name='under_probation'
                          required={false}
                          value={formik.values.under_probation}
                          onChange={(e: any) => {
                            if (e.target.value === 1) {
                              formik.setFieldValue('probation_period', 12)
                            }
                            if (e.target.value === 0) formik.values.under_probation = 0
                            else formik.values.under_probation = 1
                            return formik.handleChange(e)
                          }}
                        >
                          {/* <StyledMenuItem value=''>Select Probation Period</StyledMenuItem> */}
                          <StyledMenuItem value={1}>Yes</StyledMenuItem>
                          <StyledMenuItem value={0}>No</StyledMenuItem>
                        </SelectField>

                        <SelectField
                          size='small'
                          id='under_notice'
                          label='Under Notice'
                          select
                          name='under_notice'
                          value={formik.values.under_notice}
                          onChange={(e: any) => {
                            if (e.target.value === 0) formik.values.under_notice = 0
                            else formik.values.under_notice = 1
                            return formik.handleChange(e)
                          }}
                          sx={style.selectStyle}
                          required={false}
                        >
                          <StyledMenuItem value={1}> Yes </StyledMenuItem>
                          <StyledMenuItem value={0}> No </StyledMenuItem>
                        </SelectField>
                        {Number(formik.values.under_notice) !== 0 && (

                          <Grid item>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                              <DemoContainer
                                components={['DatePicker']}
                                sx={{ width: '100%', marginTop: '-8px', marginBottom: '-16px' }}
                              >
                                <Box sx={{ width: '92%' }}>
                                  <DatePicker
                                    label='Notice Start Date'
                                    value={
                                      formik.values.notice_date
                                        ? dayjs(formik.values.notice_date)
                                        : null
                                    }
                                    onChange={(date) => handleStartDateChange(date)}
                                    slotProps={{
                                      textField: {
                                        error:
                                          formik.touched.notice_date &&
                                          Boolean(formik.errors.notice_date),
                                      },
                                    }}
                                    sx={{
                                      border: 'none',
                                      '& .MuiOutlinedInput-root': {
                                        marginLeft: '15px',
                                        borderRadius: '20px',
                                        height: '40px',
                                        fontSize: '13px',
                                        fontFamily: styles.FONT_MEDIUM,
                                      },
                                      '& .MuiInputLabel-root': {
                                        marginLeft: '15px',
                                        fontSize: '13px',
                                        fontFamily: styles.FONT_MEDIUM,
                                        marginTop: '-5px',
                                        '& .MuiFormLabel-asterisk': {
                                          color: 'red',
                                        },
                                      },
                                    }}
                                  />
                                </Box>
                              </DemoContainer>
                            </LocalizationProvider>
                          </Grid>
                        )}

                        <SelectField
                          sx={{
                            '& .MuiFormLabel-root': {
                              marginTop: '0px !important',
                            },
                          }}
                          size='small'
                          id='relieving_type'
                          name='relieving_type'
                          label='Type Of Relieving'
                          select
                          value={formik.values.relieving_type}
                          onChange={formik.handleChange}
                          required={false}
                        >
                          <StyledMenuItem sx={{}} value={0}>
                            Select
                          </StyledMenuItem>
                          <StyledMenuItem value={2}>Absconded</StyledMenuItem>
                          <StyledMenuItem value={3}>Relieved</StyledMenuItem>
                          <StyledMenuItem value={1}>Terminated</StyledMenuItem>
                        </SelectField>
                        {formik.values.relieving_type === 3 && (
                          <SelectField
                            size='small'
                            id='final_settlement_done'
                            label='Final Settlement Done'
                            select
                            name='final_settlement_done'
                            value={formik.values.final_settlement_done}
                            onChange={formik.handleChange}
                            sx={style.selectStyle}
                            required={false}
                          >
                            <StyledMenuItem value={1}> Yes </StyledMenuItem>
                            <StyledMenuItem value={0}> No </StyledMenuItem>
                          </SelectField>
                        )}
                      </Grid>

                      <Grid item xs={5.8}>
                        <SelectField
                          size='small'
                          id='emp_type'
                          name='emp_type'
                          label='Emp Type'
                          required={false}
                          select
                          value={formik.values.emp_type}
                          onChange={formik.handleChange}
                          sx={style.selectStyle}
                        >
                          <StyledMenuItem value={1}>Salary</StyledMenuItem>
                          <StyledMenuItem value={2}>Hourly</StyledMenuItem>
                        </SelectField>

                        <SelectField
                          size='small'
                          id='service_agreement_amount'
                          name='service_agreement_amount'
                          label='Service Agreement Amount (In Rs.)'
                          select
                          required={false}
                          value={formik.values.service_agreement_amount}
                          onChange={formik.handleChange}
                          sx={style.selectStyle}
                        >
                          <StyledMenuItem value={'0'}> 0 </StyledMenuItem>
                          <StyledMenuItem value={'200000'}> 2,00,000 </StyledMenuItem>
                          <StyledMenuItem value={'10000'}> 1,00,000 </StyledMenuItem>
                          <StyledMenuItem value={'75000'}> 75,000 </StyledMenuItem>
                          <StyledMenuItem value={'50000'}> 50,000 </StyledMenuItem>
                          <StyledMenuItem value={'25000'}> 25,000 </StyledMenuItem>
                        </SelectField>

                        <SelectField
                          size='small'
                          id='probation_period'
                          name='probation_period'
                          label='Probation Period'
                          select
                          value={formik.values.probation_period}
                          onChange={formik.handleChange}
                          error={
                            formik.touched.probation_period &&
                            Boolean(formik.errors.probation_period)
                          }
                          disabled={formik.values.under_probation <= 0}
                          required={false}
                        >
                          <StyledMenuItem value={0}>NA</StyledMenuItem>
                          <StyledMenuItem value={3}>3 Months</StyledMenuItem>
                          <StyledMenuItem value={6}>6 Months</StyledMenuItem>
                          <StyledMenuItem value={9}>9 Months</StyledMenuItem>
                          <StyledMenuItem value={12}>1 Year</StyledMenuItem>
                        </SelectField>
                        <SelectField
                          size='small'
                          id='notice_period'
                          name='notice_period'
                          label='Notice Period'
                          select
                          value={formik.values.notice_period}
                          onChange={formik.handleChange}
                          required={false}
                        >
                          <StyledMenuItem value={0}>NA</StyledMenuItem>
                          <StyledMenuItem value={1}>1 Months</StyledMenuItem>
                          <StyledMenuItem value={2}>2 Months</StyledMenuItem>
                          <StyledMenuItem value={3}>3 Months</StyledMenuItem>
                          <StyledMenuItem value={6}>6 Months</StyledMenuItem>
                          <StyledMenuItem value={9}>9 Months</StyledMenuItem>
                          <StyledMenuItem value={12}>1 Year</StyledMenuItem>
                        </SelectField>
                        <TextField
                          multiline
                          fullWidth
                          variant='outlined'
                          name='notice_description'
                          label='Notice Description'
                          value={formik.values.notice_description}
                          onChange={formik.handleChange}
                          InputLabelProps={{
                            shrink: Boolean(formik.values.notice_description), // Forces label to float if value exists
                          }}
                          sx={{
                            marginLeft: '14px',
                            '& .MuiInputBase-inputMultiline': {
                              position: 'relative',
                              left: '12px',
                              top: '6px',
                            },
                            '& .MuiOutlinedInput-root': {
                              width: '92%',
                              fontSize: '13px',
                              fontFamily: 'Montserrat-Medium',
                              borderRadius: '30px',
                              minHeight: '30px',
                              padding: '6px 8px',
                              alignItems: 'center',
                            },
                            '& .MuiOutlinedInput-input': {
                              fontSize: '13px',
                              fontFamily: 'Montserrat-Medium',
                              minHeight: '30px',
                              padding: '0',
                              boxSizing: 'border-box',
                              lineHeight: '1.2',
                              '& .MuiFormLabel-asterisk': {
                                color: 'white',
                              },
                            },
                            '& .MuiInputLabel-root': {
                              marginTop: '-3px',
                              fontSize: '13px !important',
                              fontFamily: 'Montserrat-Medium !important',
                              '& .MuiFormLabel-asterisk': {
                                color: 'white',
                              },
                            },
                            '& textarea': {
                              fontSize: '13px',
                              fontFamily: 'Montserrat-Medium',
                              height: 'auto',
                              width: '95%',
                              minHeight: '35px',
                              overflowY: 'auto',
                            },
                          }}
                          disabled={formik.values.under_notice <= 0}
                        />
                        {Number(formik.values.under_notice) !== 0 && (
                          <Grid item>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                              <DemoContainer
                                components={['DatePicker']}
                                sx={{ width: '100%', marginTop: '-20px', marginBottom: '-16px' }}
                              >
                                <Box sx={{ width: '92%' }}>
                                  <DatePicker
                                    label='Notice End Date'
                                    value={
                                      formik.values?.tentative_termination_date
                                        ? dayjs(formik.values.tentative_termination_date)
                                        : null
                                    }
                                    onChange={(date: any) => {
                                      formik.setFieldValue(
                                        'tentative_termination_date',
                                        date?.format('YYYY-MM-DD'),
                                      )
                                    }}
                                    shouldDisableDate={(date) => {
                                      const startDate = formik.values.notice_date
                                        ? dayjs(formik.values.notice_date)
                                        : null
                                      return startDate ? date.isBefore(startDate) : false
                                    }}
                                    slotProps={{
                                      textField: {
                                        error:
                                          formik.touched.tentative_termination_date &&
                                          Boolean(formik.errors.tentative_termination_date),
                                        helperText:
                                          formik.touched.tentative_termination_date &&
                                          formik.errors.tentative_termination_date,
                                      },
                                    }}
                                    sx={{
                                      border: 'none',
                                      '& .MuiOutlinedInput-root': {
                                        marginLeft: '15px',
                                        borderRadius: '20px',
                                        height: '40px',
                                        fontSize: '13px',
                                        fontFamily: styles.FONT_MEDIUM,
                                      },
                                      '& .MuiInputLabel-root': {
                                        marginLeft: '15px',
                                        fontSize: '13px',
                                        fontFamily: styles.FONT_MEDIUM,
                                        marginTop: '-5px',
                                        '& .MuiFormLabel-asterisk': {
                                          color: 'red',
                                        },
                                      },
                                    }}
                                  />
                                </Box>
                              </DemoContainer>
                            </LocalizationProvider>
                          </Grid>
                        )}
                        {Number(formik.values.relieving_type) > 0 && (
                          <>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                              <DemoContainer
                                components={['DatePicker']}
                                sx={{ width: '100%', marginTop: '-23px' }}
                              >
                                <Box sx={{ width: '92%' }}>
                                  <DatePicker
                                    sx={{
                                      border: 'none',
                                      '& .MuiOutlinedInput-root': {
                                        marginLeft: '15px',
                                        borderRadius: '20px',
                                        height: '40px',
                                        fontSize: '13px', // Adjust the font size here
                                        fontFamily: styles.FONT_MEDIUM,
                                      },
                                      '& .MuiInputLabel-root': {
                                        marginLeft: '15px',
                                        fontSize: '13px', // Adjust the font size here
                                        fontFamily: styles.FONT_MEDIUM,
                                        marginTop: '-5px',
                                        '& .MuiFormLabel-asterisk': {
                                          color: 'red',
                                        },
                                      },
                                    }}
                                    label='Last Date Of Work'
                                    value={
                                      formik.values.last_day_of_working
                                        ? dayjs(formik.values.last_day_of_working)
                                        : null
                                    }
                                    onChange={(date: any) => {
                                      formik.setFieldValue(
                                        'last_day_of_working',
                                        date?.format('YYYY-MM-DD'),
                                      )
                                    }}
                                    slotProps={{
                                      textField: {
                                        error:
                                          formik.touched.last_day_of_working &&
                                          Boolean(formik.errors.last_day_of_working),
                                      },
                                    }}
                                  />
                                </Box>
                              </DemoContainer>
                            </LocalizationProvider>
                            <TextField
                              label='Comment from HR'
                              name='comment_from_HR'
                              fullWidth
                              multiline
                              sx={{
                                marginTop: '0px',
                                marginLeft: '14px',
                                '& .MuiInputBase-inputMultiline': {
                                  position: 'relative',
                                  left: '12px',
                                  top: '6px',
                                },
                                '& .MuiOutlinedInput-root': {
                                  width: '92%',
                                  fontSize: '13px',
                                  fontFamily: 'Montserrat-Medium',
                                  borderRadius: '30px',
                                  minHeight: '30px',
                                  padding: '6px 8px',
                                  alignItems: 'center',
                                },
                                '& .MuiOutlinedInput-input': {
                                  fontSize: '13px',
                                  fontFamily: 'Montserrat-Medium',
                                  minHeight: '30px',
                                  padding: '0',
                                  boxSizing: 'border-box',
                                  lineHeight: '1.2',
                                  '& .MuiFormLabel-asterisk': {
                                    color: 'white',
                                  },
                                },
                                '& .MuiInputLabel-root': {
                                  marginTop: '-3px',
                                  fontSize: '13px !important',
                                  fontFamily: 'Montserrat-Medium !important',
                                  '& .MuiFormLabel-asterisk': {
                                    color: 'white',
                                  },
                                },
                                '& textarea': {
                                  fontSize: '13px',
                                  fontFamily: 'Montserrat-Medium',
                                  height: 'auto',
                                  width: '95%',
                                  minHeight: '35px',
                                  overflowY: 'auto',
                                },
                              }}
                              value={formik.values.comment_from_HR}
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                              error={
                                formik.touched.comment_from_HR &&
                                Boolean(formik.errors.comment_from_HR)
                              }
                            />
                          </>
                        )}
                      </Grid>
                    </Box>

                    <Box
                      height={
                        formik.values.poor_performance_review == '1' &&
                          formik.values.under_maternity == '1'
                          ? '305px'
                          : (formik.values.poor_performance_review == '0' &&
                            formik.values.under_maternity == '1') ||
                            (formik.values.poor_performance_review == '1' &&
                              formik.values.under_maternity == '0')
                            ? '190px'
                            : formik.values.poor_performance_review == '0' &&
                              formik.values.under_maternity == '0'
                              ? '70px'
                              : 'auto'
                      }
                    ></Box>
                    <Box height={Number(formik.values.relieving_type) === 0 ? '72px' : '0'}></Box>
                  </Grid>
                </Grid>
              </Box>

              {/* Second Section */}
              <Box width='31%'>
                <Grid sx={{ backgroundColor: 'white', borderRadius: '8px', border: 'none' }}>
                  <Typography
                    padding={'10px 10px'} // Retain padding inside the Typography
                    sx={{
                      fontFamily: styles.FONT_BOLD,
                      fontSize: '16px', // Use quotes around the size
                      fontWeight: 700, // Use camelCase for font-weight
                      lineHeight: '19.36px', // Use quotes around the line height
                      textAlign: 'left', // Use camelCase for text-align
                      borderBottom: '1px solid #193C6C',
                      color: '#193C6C',
                    }}
                  >
                    Other Info
                  </Typography>{' '}
                  <Box>
                    <Grid item>
                      <DepartmentDropdown
                        userDepartment={userDepartment}
                        handleUserDepartmentChange={handleUserDepartmentChange}
                        fetchDepartmentDataFetch={fetchDepartmentDataFetch}
                        hasPermission={hasPermission}
                        userData={getEmployeeInfo}
                      />
                    </Grid>
                    <Grid item>
                      <SelectField
                        sx={{
                          '& .MuiFormLabel-root': {
                            marginTop: '0px !important',
                          },
                        }}
                        size='small'
                        id='first_performance_review'
                        name='first_performance_review'
                        label='First Performance Review'
                        select
                        value={formik.values.first_performance_review}
                        onChange={formik.handleChange}
                        required={false}
                      >
                        <StyledMenuItem value={'1'}>Yes</StyledMenuItem>
                        <StyledMenuItem value={'0'}>No</StyledMenuItem>
                      </SelectField>
                    </Grid>
                    <Grid item>
                      <SelectField
                        size='small'
                        id='first_weekend'
                        name='first_weekend'
                        label='First Weekend'
                        select
                        value={formik.values.first_weekend}
                        onChange={formik.handleChange}
                        sx={style.selectStyle}
                        error={formik.touched.first_weekend && Boolean(formik.errors.first_weekend)}
                        required={true}
                      >
                        <StyledMenuItem value={'Sunday'}> Sunday </StyledMenuItem>
                        <StyledMenuItem value={'Monday'}> Monday </StyledMenuItem>
                        <StyledMenuItem value={'Tuesday'}> Tuesday </StyledMenuItem>
                        <StyledMenuItem value={'Wednesday'}> Wednesday </StyledMenuItem>
                        <StyledMenuItem value={'Thursday'}> Thursday </StyledMenuItem>
                        <StyledMenuItem value={'Friday'}> Friday </StyledMenuItem>
                        <StyledMenuItem value={'Saturday'}> Saturday </StyledMenuItem>
                      </SelectField>
                    </Grid>
                    <Grid item>
                      <SelectField
                        size='small'
                        id='second_weekend'
                        name='second_weekend'
                        label='Second Weekend'
                        select
                        value={formik.values.second_weekend}
                        onChange={formik.handleChange}
                        sx={style.selectStyle}
                        error={formik.touched.second_weekend && Boolean(formik.errors.second_weekend)}
                      >
                        <StyledMenuItem value={'NA'}> NA </StyledMenuItem>
                        <StyledMenuItem value={'Sunday'}> Sunday </StyledMenuItem>
                        <StyledMenuItem value={'Monday'}> Monday </StyledMenuItem>
                        <StyledMenuItem value={'Tuesday'}> Tuesday </StyledMenuItem>
                        <StyledMenuItem value={'Wednesday'}> Wednesday </StyledMenuItem>
                        <StyledMenuItem value={'Thursday'}> Thursday </StyledMenuItem>
                        <StyledMenuItem value={'Friday'}> Friday </StyledMenuItem>
                        <StyledMenuItem value={'Saturday'}> Saturday </StyledMenuItem>
                      </SelectField>
                    </Grid>
                    <Grid item sx={{ display: 'flex', justifyContent: 'center' }}>
                      <Autocomplete
                        autoFocus={false}
                        size='small'
                        disablePortal
                        clearIcon={null}
                        id='select-referred-by'
                        options={props.AllEmpList || []}
                        getOptionLabel={(option) => option.name || ''}
                        isOptionEqualToValue={(option, value) => option?.userId === value?.userId}
                        onChange={handleAutocompleteChange}
                        renderInput={(params) => (
                          <TextField
                            sx={{
                              '& .MuiFormLabel-root.MuiInputLabel-root': {
                                fontSize: '13px', // Adjust the font size here
                                fontFamily: styles.FONT_MEDIUM,
                                lineHeight: '1.8em',
                              },
                            }}
                            {...params}
                            required={false}
                            label='Referred By'
                            variant='outlined'
                          />
                        )}
                        value={formik?.values?.referred_by}
                        ListboxProps={{
                          style: {
                            maxHeight: '150px',
                            overflowY: 'auto',
                          },
                        }}
                        sx={{
                          width: '92%',
                          marginBottom: 0,
                          '& label.Mui-focused': {
                            color: '#666666',
                          },
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderRadius: '20px',
                              fontFamily: styles.FONT_MEDIUM,
                            },

                            '& fieldset': {
                              borderRadius: '20px',
                            },
                            '& .MuiInputBase-input': {
                              borderRadius: '15px',
                              width: 'auto',
                              fontFamily: styles.FONT_MEDIUM,
                              letterSpacing: '0',
                              fontSize: '13px',
                            },
                          },
                          svg: {
                            color: '#666666',
                          },
                        }}
                      />
                    </Grid>
                    <Grid item sx={{ marginTop: '-15px' }}>
                      <SelectField
                        size='small'
                        id='isIDCard'
                        name='isIDCard'
                        label='Id Card Issued'
                        select
                        value={formik.values.isIDCard}
                        onChange={formik.handleChange}
                        required={false}
                        sx={style.selectStyle}
                      >
                        <StyledMenuItem value={'1'}>Yes</StyledMenuItem>
                        <StyledMenuItem value={'0'}>No</StyledMenuItem>
                      </SelectField>
                    </Grid>
                    <Grid item>
                      <SelectField
                        size='small'
                        id='poor_performance_review'
                        name='poor_performance_review'
                        label='Poor Performance Review'
                        select
                        value={formik.values.poor_performance_review}
                        onChange={(e: any) => handlePoorPerformance(e.target.value)}
                        required={false}
                        sx={style.selectStyle}
                      >
                        <StyledMenuItem value={'1'}>Yes</StyledMenuItem>
                        <StyledMenuItem value={'0'}>No</StyledMenuItem>
                      </SelectField>
                    </Grid>
                    {Number(formik.values.poor_performance_review) !== 0 && (
                      <>
                        <Grid item>
                          <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DemoContainer
                              components={['DatePicker']}
                              sx={{ width: '100%', marginTop: '-8px', marginBottom: '-16px' }}
                            >
                              <Box sx={{ width: '92%' }}>
                                <DatePicker
                                  sx={{
                                    border: 'none',
                                    '& .MuiOutlinedInput-root': {
                                      marginLeft: '15px',
                                      borderRadius: '20px',
                                      height: '40px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                    },
                                    '& .MuiInputLabel-root': {
                                      marginLeft: '15px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                      marginTop: '-5px',
                                      '& .MuiFormLabel-asterisk': {
                                        color: 'red',
                                      },
                                    },
                                  }}
                                  label='Start Date'
                                  value={
                                    formik.values.from_date ? dayjs(formik.values.from_date) : null
                                  }
                                  onChange={(date: any) => {
                                    formik.setFieldValue('from_date', date?.format('YYYY-MM-DD'))
                                  }}
                                  slotProps={{
                                    textField: {
                                      error:
                                        formik.touched.from_date &&
                                        Boolean(formik.errors.from_date),
                                    },
                                  }}
                                />
                              </Box>
                            </DemoContainer>
                          </LocalizationProvider>
                        </Grid>

                        <Grid item>
                          <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DemoContainer components={['DatePicker']} sx={{ width: '100%' }}>
                              <Box sx={{ width: '92%' }}>
                                <DatePicker
                                  sx={{
                                    border: 'none',
                                    '& .MuiOutlinedInput-root': {
                                      marginLeft: '15px',
                                      borderRadius: '20px',
                                      height: '40px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                    },
                                    '& .MuiInputLabel-root': {
                                      marginLeft: '15px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                      marginTop: '-5px',
                                      '& .MuiFormLabel-asterisk': {
                                        color: 'red',
                                      },
                                    },
                                  }}
                                  label='End Date'
                                  value={
                                    formik.values.to_date ? dayjs(formik.values.to_date) : null
                                  }
                                  onChange={(date: any) => {
                                    formik.setFieldValue('to_date', date?.format('YYYY-MM-DD'))
                                  }}
                                  slotProps={{
                                    textField: {
                                      error:
                                        formik.touched.to_date && Boolean(formik.errors.to_date),
                                    },
                                  }}
                                />
                              </Box>
                            </DemoContainer>
                          </LocalizationProvider>
                        </Grid>
                      </>
                    )}
                    <Grid
                      item
                      sx={{
                        marginTop: formik.values.poor_performance_review == '0' ? 0 : '-15px',
                      }}
                    >
                      <SelectField
                        size='small'
                        id='under_maternity'
                        name='under_maternity'
                        label='Under Maternity'
                        select
                        value={formik.values.under_maternity}
                        required={false}
                        onChange={(e: any) => handleMaternity(e.target.value)}
                      >
                        <StyledMenuItem value={'1'}>Yes</StyledMenuItem>
                        <StyledMenuItem value={'0'}>No</StyledMenuItem>
                      </SelectField>
                    </Grid>
                    {Number(formik.values.under_maternity) !== 0 && (
                      <>
                        <Grid item>
                          <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DemoContainer
                              components={['DatePicker']}
                              sx={{ width: '100%', marginTop: '-8px', marginBottom: '-16px' }}
                            >
                              <Box sx={{ width: '92%' }}>
                                <DatePicker
                                  sx={{
                                    border: 'none',
                                    '& .MuiOutlinedInput-root': {
                                      marginLeft: '15px',
                                      borderRadius: '20px',
                                      height: '40px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                    },
                                    '& .MuiInputLabel-root': {
                                      marginLeft: '15px',
                                      fontSize: '13px', // Adjust the font size here
                                      fontFamily: styles.FONT_MEDIUM,
                                      marginTop: '-5px',
                                      '& .MuiFormLabel-asterisk': {
                                        color: 'red',
                                      },
                                    },
                                  }}
                                  label='Maternity Start Date'
                                  value={
                                    formik.values.maternity_start_date
                                      ? dayjs(formik.values.maternity_start_date)
                                      : null
                                  }
                                  onChange={(date: any) => {
                                    formik.setFieldValue(
                                      'maternity_start_date',
                                      date?.format('YYYY-MM-DD'),
                                    )
                                  }}
                                  slotProps={{
                                    textField: {
                                      error:
                                        formik.touched.maternity_start_date &&
                                        Boolean(formik.errors.maternity_start_date),
                                    },
                                  }}
                                />
                              </Box>
                            </DemoContainer>
                          </LocalizationProvider>
                        </Grid>

                        <Grid item>
                          <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DemoContainer
                              components={['DatePicker']}
                              sx={{ width: '100%', marginTop: '-20px', marginBottom: '-16px' }}
                            >
                              <Box sx={{ width: '92%' }}>
                                <DatePicker
                                  label='Maternity End Date'
                                  value={
                                    formik.values.maternity_end_date
                                      ? dayjs(formik.values.maternity_end_date)
                                      : null
                                  }
                                  onChange={(date: any) => {
                                    formik.setFieldValue(
                                      'maternity_end_date',
                                      date?.format('YYYY-MM-DD'),
                                    )
                                  }}
                                  shouldDisableDate={(date) => {
                                    const startDate = formik.values.maternity_start_date
                                      ? dayjs(formik.values.maternity_start_date)
                                      : null
                                    return startDate ? date.isBefore(startDate) : false // Ensure the function always returns a boolean
                                  }}
                                  slotProps={{
                                    textField: {
                                      error:
                                        formik.touched.maternity_end_date &&
                                        Boolean(formik.errors.maternity_end_date),
                                    },
                                  }}
                                  sx={{
                                    border: 'none',
                                    '& .MuiOutlinedInput-root': {
                                      marginLeft: '15px',
                                      borderRadius: '20px',
                                      height: '40px',
                                      fontSize: '13px',
                                      fontFamily: styles.FONT_MEDIUM,
                                    },
                                    '& .MuiInputLabel-root': {
                                      marginLeft: '15px',
                                      fontSize: '13px',
                                      fontFamily: styles.FONT_MEDIUM,
                                      marginTop: '-5px',
                                      '& .MuiFormLabel-asterisk': {
                                        color: 'red',
                                      },
                                    },
                                  }}
                                />
                              </Box>
                            </DemoContainer>
                          </LocalizationProvider>
                        </Grid>
                      </>
                    )}
                    <Grid item>
                      <SelectField
                        size='small'
                        id='isMediclaim'
                        label='Mediclaim Issued'
                        select
                        name='isMediclaim'
                        value={formik.values.isMediclaim}
                        onChange={formik.handleChange}
                        sx={style.selectStyle}
                        required={false}
                      >
                        <StyledMenuItem value={'1'}> Yes </StyledMenuItem>
                        <StyledMenuItem value={'0'}> No </StyledMenuItem>
                      </SelectField>
                    </Grid>

                    <Grid item>
                      <InputField
                        size='small'
                        id='mediclaim_no'
                        name='mediclaim_no'
                        label='Mediclam No'
                        value={formik.values.mediclaim_no}
                        required={false}
                        onChange={formik.handleChange}
                        disabled={formik.values.isMediclaim != '1'}
                      />{' '}
                    </Grid>
                    <Grid item>
                      <InputField
                        size='small'
                        id='passport_no'
                        name='passport_no'
                        label='Passport No'
                        value={formik.values.passport_no}
                        required={false}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    <Grid item>
                      <SelectField
                        size='small'
                        id='tshirt_size'
                        name='tshirt_size'
                        label='Tshirt Size'
                        value={formik.values.tshirt_size}
                        select
                        onChange={formik.handleChange}
                        sx={style.selectStyle}
                        required={false}
                      >
                        <StyledMenuItem value={0} disabled>
                          {' '}
                          Select{' '}
                        </StyledMenuItem>
                        <StyledMenuItem value={1}> S </StyledMenuItem>
                        <StyledMenuItem value={2}> M </StyledMenuItem>
                        <StyledMenuItem value={3}> L </StyledMenuItem>
                        <StyledMenuItem value={4}> XL </StyledMenuItem>
                        <StyledMenuItem value={5}> XXL </StyledMenuItem>
                        <StyledMenuItem value={6}> XXXL </StyledMenuItem>
                      </SelectField>
                    </Grid>
                    {/* <Grid item>
                    <InputField
                      size='small'
                      fullWidth
                      id='time_to_visit'
                      name='time_to_visit'
                      label='Time To Visit'
                      type='time'
                      value={formik.values.time_to_visit || ''}
                      onChange={formik.handleChange}
                      InputLabelProps={{ shrink: true }}
                      required={false}
                    />
                  </Grid>
                  <Grid item>
                    <SelectField
                      size='small'
                      id='day_to_visit'
                      name='day_to_visit'
                      label='Day To Visit'
                      select
                      value={formik.values.day_to_visit}
                      onChange={formik.handleChange}
                      error={formik.touched.day_to_visit && Boolean(formik.errors.day_to_visit)}
                      sx={style.selectStyle}
                      required={false}
                    >
                      {daysOfWeek?.map((day: any) => (
                        <StyledMenuItem key={day.id} value={day.value}>
                          {day.value}
                        </StyledMenuItem>
                      ))}
                    </SelectField>
                  </Grid> */}
                    <Grid item>
                      <SelectField
                        size='small'
                        id='shift_timing'
                        name='shift_timing'
                        label='Timing'
                        value={formik.values.shift_timing}
                        select
                        onChange={formik.handleChange}
                        sx={style.selectStyle}
                        required={false}
                      >
                        {/* {props?.timingData?.map((data: any) => {
                        return (
                          <StyledMenuItem value={data?.id}>
                            {moment(data?.timing).utcOffset('+05:30').format('h:mm A')}
                            </StyledMenuItem>
                        )
                      })} */}
                        {props?.timingData?.map((data: any) => {
                          return (
                            <StyledMenuItem value={data?.id}>
                              {' '}
                              {moment(data?.timing).utc().format('h:mm A')}
                            </StyledMenuItem>
                          )
                        })}
                      </SelectField>
                    </Grid>

                    <Grid item>
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DemoContainer
                          components={['DatePicker']}
                          sx={{ width: '100%', marginTop: '-8px' }}
                        >
                          <Box sx={{ width: '92%' }}>
                            <DatePicker
                              sx={{
                                border: 'none',
                                '& .MuiOutlinedInput-root': {
                                  marginLeft: '15px',
                                  borderRadius: '20px',
                                  height: '40px',
                                  fontSize: '13px', // Adjust the font size here
                                  fontFamily: styles.FONT_MEDIUM,
                                },
                                '& .MuiInputLabel-root': {
                                  marginLeft: '15px',
                                  fontSize: '13px', // Adjust the font size here
                                  fontFamily: styles.FONT_MEDIUM,
                                  marginTop: '-5px',
                                  '& .MuiFormLabel-asterisk': {
                                    color: 'red',
                                  },
                                },
                              }}
                              label='Effective Date'
                              value={
                                formik.values.start_date ? dayjs(formik.values.start_date) : null
                              }
                              onChange={(date: any) => {
                                formik.setFieldValue('start_date', date?.format('YYYY-MM-DD'))
                              }}
                              slotProps={{
                                textField: {
                                  error:
                                    formik.touched.start_date && Boolean(formik.errors.start_date),
                                },
                              }}
                            />
                          </Box>
                        </DemoContainer>
                      </LocalizationProvider>
                    </Grid>
                  </Box>
                </Grid>
              </Box>
            </Box>
            <div
              style={{
                marginTop: '24px',
                marginRight: '15px',
                display: 'flex',
                justifyContent: 'end',
                alignItems: 'center',
                gap: '10px',
                margin: '20px',
              }}
            >
              <Button
                sx={{
                  fontSize: '16px !important',
                  fontFamily: `${styles.FONT_BOLD}!important`,
                  width: '20%',
                  borderRadius: '20px !important',
                  cursor: 'pointer',
                  height: '40px',
                  padding: '14px 28px',
                  opacity: 1,
                  background: '#E2E2E2',
                  color: '#193C6C',
                  '&:hover': {
                    background: '#E2E2E2',
                    color: '#000000',
                  },
                }}
                variant='contained'
                onClick={() => handleReset()}
              >
                Cancel
              </Button>
              <CommonButton variant='contained' color='primary' onClick={handleSubmit}>
                FINISH
              </CommonButton>
            </div>
          </div>
        </Box>
      </form>
    </div>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    isGetEmpoyeementInfoLoader: fetchUserDetailsUI.fetchUserData(state).isGetEmpoyeementInfoLoader,
    fetchDepartmentDataFetch: SREntity.fetchServiceRequestDepartment(state).getServiceRequestDepartment,
    employeementInfoData: fetchUserDetailsEntities.fetchUserData(state).getEmployeementInfo,
    isEmployeementInfoUpdated: fetchUserDetailsUI.fetchUserData(state).isGetEmpoyeementInfo,
    designationData: employeePortalEntity.getEmployeePortal(state).getDesignationData,
    designationBandData: employeePortalEntity.getEmployeePortal(state).getDesignationBandData,
    AllEmpList: projectManagementEntity.getAllProjectManagement(state).getAllEmpList,
    getUserDetails: fetchUserDetailsEntities.fetchUserData(state).fetchUser,
    allRoles: fetchUserDetailsEntities.fetchUserData(state).fetchAllRole,
    allStates: fetchUserDetailsEntities.fetchUserData(state).fetchAllState,
    allCountries: fetchUserDetailsEntities.fetchUserData(state).fetchAllCountry,
    allFloors: fetchUserDetailsEntities.fetchUserData(state).fetchAllFloor,
    allWorkstations: fetchUserDetailsEntities.fetchUserData(state).fetchAllWorkstation,
    allLocation: fetchUserDetailsEntities.fetchUserData(state).fetchAllLocation,
    allClientLocation: fetchUserDetailsEntities.fetchUserData(state).fetchAllClientLocation,
    isUserCreateFailed: fetchUserDetailsUI.fetchUserData(state).isUserCreateFailed,
    isUserUpdated: fetchUserDetailsUI.fetchUserData(state).isUserUpdated,
    isUserUpdating: fetchUserDetailsUI.fetchUserData(state).isUserUpdating,
    isUserUpdateFailed: fetchUserDetailsUI.fetchUserData(state).isUserUpdateFailed,
    userImage: fetchUserDetailsEntities.fetchUserData(state).getUserImage,
    timingData: employeePortalEntity.getEmployeePortal(state).getTimingData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    serviceRequestDepartment: (data: any) => dispatch(fetchServiceRequestDepartment.request(data)),
    fetchDesignationData: () => dispatch(fetchDesignation.request()),
    fetchDesignationBandData: () => dispatch(fetchDesignationBand.request()),
    fetchAllEmpAPI: () => dispatch(fetchWorkingEmp.request()),
    getUserImage: (data: any) => dispatch(getUserImage.request(data)),
    resetUserImage: () => {
      dispatch(getUserImage.reset())
    },
    uploadUserImg: (data: any) => dispatch(uploadUserImg.request({ data })),
    createUser: (data: any) => dispatch(createUser.request({ data })),

    fetchEmployeementInfo: (data: any) => dispatch(fetchEmployeeInfo.request({ data })),
    resetEmployeementInfo: () => dispatch(fetchEmployeeInfo.reset()),
    resetCreateUser: () => {
      dispatch(createUser.reset())
    },
    fetchAllRole: () => dispatch(fetchAllRoles.request()),
    fetchAllState: () => dispatch(fetchAllStates.request()),
    fetchAllCountry: () => dispatch(fetchAllCountries.request()),
    fetchAllFloor: () => dispatch(fetchAllFloors.request()),
    fetchAllWorkstation: () => dispatch(fetchAllWorkstation.request()),
    fetchAllLocation: () => dispatch(fetchAllLocation.request()),
    updateUser: (data: any) => dispatch(updateUser.request({ data })),
    resetUpdateUser: () => {
      dispatch(updateUser.reset())
    },
    fetchAllClientLocation: () => dispatch(fetchAllClientLocation.request()),
    fetchTimingData: () => dispatch(fetchTiming.request()),
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(EmpoloyeementInfo)
