import { <PERSON><PERSON>, <PERSON>, Tabs, Tab } from '@mui/material'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

interface HeaderProps {
  setOpen: (value: boolean) => void
}

const HeaderSection: React.FC<HeaderProps> = ({ setOpen }) => {
  return (
    <>
      <Box
        display='flex'
        alignItems='center'
        justifyContent='space-between'
        mb={2}
        marginTop={'10px'}
      >
        <Box className='templates-page-tabs'>
          <Tabs value={0} aria-label='Tabs for different tables'>
            <Tab label='Schedules' />
          </Tabs>
        </Box>
        <ActionButton onClick={() => setOpen(true)}>Add Schedule</ActionButton>
      </Box>
    </>
  )
}

export default HeaderSection
