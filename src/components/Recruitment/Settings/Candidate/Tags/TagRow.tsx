import React, { useEffect } from 'react'
import { TableRow, TableCell, IconButton } from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { Tag } from './TagsTable'
import { StyledTableCellForMyTeam } from 'components/Common/CommonStyles'


interface TagRowProps {
  tag: Tag
  index: number
  setEditIndex: React.Dispatch<React.SetStateAction<number | null>>
  setEditTag: React.Dispatch<React.SetStateAction<Tag>>
  setDeleteIndex: React.Dispatch<React.SetStateAction<number | null>>

}

const TagRow: React.FC<TagRowProps> = ({
  tag,
  index,
  setEditIndex,
  setEditTag,
  setDeleteIndex,
}) => {
  return (
    <TableRow key={index}>
      <StyledTableCellForMyTeam>{tag.name}</StyledTableCellForMyTeam>
      <StyledTableCellForMyTeam>{tag.candidates}</StyledTableCellForMyTeam>
      <StyledTableCellForMyTeam>{tag.is_shortcut_tag ? 'Yes' : 'No'}</StyledTableCellForMyTeam>
      <StyledTableCellForMyTeam>
        <IconButton
          color='primary'
          onClick={() => {
            setEditIndex(tag.id)
            setEditTag(tag)
          }}
        >
          <EditIcon />
        </IconButton>
        <IconButton
          sx={{ color: '#db3700' }}
          onClick={() => {
            setDeleteIndex(tag.id)
          }}
        >
          <DeleteIcon />
        </IconButton>
      </StyledTableCellForMyTeam>
    </TableRow>
  )
}



export default TagRow