import React from 'react'
import { Box, Pagination } from '@mui/material'
import { Tag } from './TagsTable'

interface PaginationControlProps {
  page: number
  handlePageChange: (event: React.ChangeEvent<unknown>, value: number) => void
  filteredTags: Tag[]
  rowsPerPage: number
}

const PaginationControl: React.FC<PaginationControlProps> = ({
  page,
  handlePageChange,
  filteredTags,
  rowsPerPage,
}) => {
  const pageCount = Math.ceil(filteredTags.length / rowsPerPage)

  return (
    <Box sx={{display:'flex',justifyContent:'flex-end' , pt:'10px'}}>
    <Pagination count={pageCount} page={page } onChange={handlePageChange} color='primary' siblingCount={0} boundaryCount={1}/>
    </Box>
  )
}

export default PaginationControl