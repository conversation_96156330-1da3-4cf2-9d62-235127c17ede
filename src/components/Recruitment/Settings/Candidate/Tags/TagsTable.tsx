import React, { useEffect, useState } from 'react'
import { Box, Paper } from '@mui/material'
import TagDialog from './TagDialog'
import SearchBar from './SearchBar'
import PaginationControl from './PaginationControlled'
import Header from './Header'
import TableContainer from './TableContainer'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { RootState } from 'configureStore'
import { recruitmentEntity, recruitmentStateUI } from 'reducers'
import { fetchTagCandidates, getNewTag } from 'actions'

export interface Tag {
  id: number | null
  name: string
  candidates: number
  is_shortcut_tag: boolean
}

function TagsTable(props: any) {
  const [tags, setTags] = useState<Tag[]>([])
  const [page, setPage] = useState<number>(1)
  const rowsPerPage: number = 10
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [editTag, setEditTag] = useState<Tag>({
    id: null,
    name: '',
    candidates: 0,
    is_shortcut_tag: false,
  })
  const [addTagOpen, setAddTagOpen] = useState(false)
  const [newTag, setNewTag] = useState<Tag>({
    id: null,
    name: '',
    candidates: 0,
    is_shortcut_tag: false,
  })
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [isClicked, setIsClicked] = useState(false)
  const { fetchTagCandidateData, tagCandidateOptions, isAddTag, isEditTag, isDeleteTag } = props

  useEffect(() => {
    fetchTagCandidateData()
  }, [fetchTagCandidateData, isAddTag, isEditTag, isDeleteTag])

  useEffect(() => {
    if (Array.isArray(tagCandidateOptions) && tagCandidateOptions.length > 0) {
      setTags(tagCandidateOptions[0])
    }
  }, [tagCandidateOptions])

  const handleClick = () => {
    setIsClicked(true)
    setTimeout(() => setIsClicked(false), 200)
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleDeleteConfirm = () => {
    if (deleteIndex !== null) {
      const updatedTags = tags.filter((_, i) => i !== deleteIndex)
      setTags(updatedTags)
      setDeleteIndex(null)
      if ((page - 1) * rowsPerPage >= updatedTags.length) {
        setPage((prev) => Math.max(prev - 1, 1))
      }
    }
  }

  const handleEditSave = () => {
    if (editIndex !== null) {
      const updatedTags = [...tags]
      updatedTags[editIndex] = editTag
      setTags(updatedTags)
      setEditIndex(null)
    }
  }

  const handleAddTag = () => {
    const updatedTags = [...tags, newTag]
    setTags(updatedTags)
    props.addNewTag(newTag)
    setAddTagOpen(false)
    setNewTag({ id: null, name: '', candidates: 0, is_shortcut_tag: false })
  }

  const filteredTags = tags.filter((tag) =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <Box sx={{ p: 3, fontFamily: 'Montserrat-Medium' }}>
      <Paper elevation={5} sx={{ padding: '20px', backgroundColor: 'white' }}>
        <Header isClicked={isClicked} handleClick={handleClick} />
        <SearchBar
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          setAddTagOpen={setAddTagOpen}
        />

        <Box mt={3}>
          <TableContainer
            tags={filteredTags}
            page={page}
            rowsPerPage={rowsPerPage}
            setEditIndex={setEditIndex}
            setEditTag={setEditTag}
            setDeleteIndex={setDeleteIndex}
          />
        </Box>
        <PaginationControl
          page={page}
          handlePageChange={handlePageChange}
          filteredTags={filteredTags}
          rowsPerPage={rowsPerPage}
        />
        <TagDialog
          deleteIndex={deleteIndex}
          setDeleteIndex={setDeleteIndex}
          handleDeleteConfirm={handleDeleteConfirm}
          editIndex={editIndex}
          setEditIndex={setEditIndex}
          editTag={editTag}
          setEditTag={setEditTag}
          handleEditSave={handleEditSave}
          addTagOpen={addTagOpen}
          setAddTagOpen={setAddTagOpen}
          newTag={newTag}
          setNewTag={setNewTag}
          handleAddTag={handleAddTag}
        />
      </Paper>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    tagCandidateOptions: recruitmentEntity.getRecruitment(state).getTagCandidatesData,
    isAddTag: recruitmentStateUI.getRecruitment(state).addNewTag,
    isEditTag: recruitmentStateUI.getRecruitment(state).editNewTag,
    isDeleteTag: recruitmentStateUI.getRecruitment(state).deleteTag,
  }
}
const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchTagCandidateData: () => dispatch(fetchTagCandidates.request()),
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(TagsTable)