import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  DialogContentText,
  DialogActions,
  TextField,
  Box,
  FormControlLabel,
  Checkbox,
} from '@mui/material'
import { Tag } from './TagsTable'
import { RootState } from 'configureStore'
import { recruitmentEntity, recruitmentStateUI } from 'reducers'
import { Dispatch } from 'redux'
import { editTag, getDeleteTag, getNewTag } from 'actions'
import { connect } from 'react-redux'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

interface TagDialogProps {
  deleteIndex: number | null
  setDeleteIndex: React.Dispatch<React.SetStateAction<number | null>>
  handleDeleteConfirm: () => void
  editIndex: number | null
  setEditIndex: React.Dispatch<React.SetStateAction<number | null>>
  editTag: Tag
  setEditTag: React.Dispatch<React.SetStateAction<Tag>>
  addTagOpen: boolean
  setAddTagOpen: React.Dispatch<React.SetStateAction<boolean>>
  newTag: Tag
  setNewTag: React.Dispatch<React.SetStateAction<Tag>>
  handleAddTag: () => void
  addNewTagOptions: any
  fetchAddNewTag: (data: {}) => void
  fetchEditTag: (data: {}) => void
  handleEditSave: (data: {}) => void
  fetchDeleteTag: (data: { id: number | null }) => void
}

const TagDialog: React.FC<TagDialogProps> = ({
  deleteIndex,
  setDeleteIndex,
  editIndex,
  setEditIndex,
  editTag,
  setEditTag,
  addTagOpen,
  setAddTagOpen,
  newTag,
  setNewTag,
  fetchAddNewTag,
  fetchEditTag,
  fetchDeleteTag,
}) => {
  const handleSave = () => {
    fetchAddNewTag({
      tag: newTag.name,
      is_shortcut_tag: newTag.is_shortcut_tag,
    })
    setAddTagOpen(false)
  }

  const handleAddTag = () => {
    fetchEditTag({
      id: editIndex,
      name: editTag.name,
      is_shortcut_tag: editTag.is_shortcut_tag,
    })
    setEditIndex(null)
  }

  const handleDeleteConfirm = () => {
    if (deleteIndex !== null) {
      fetchDeleteTag({ id: deleteIndex })
      setDeleteIndex(null)
    }
  }

  return (
    <>
      <Dialog
        open={deleteIndex !== null}
        onClose={() => setDeleteIndex(null)}
        fullWidth
        maxWidth='sm'
      >
        <DialogTitle
          sx={{
            fontFamily: 'Montserrat-SemiBold',
            padding: '20px',
            fontSize: '30px',
            color: '#ffffff',
            display: 'flex',
            justifyContent: 'center',
            backgroundColor: '#193C6D',
          }}
        >
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <DialogContentText
            sx={{ marginLeft: '2px', color: '#193C6D', fontSize: '20px', marginTop: '10px' }}
          >
            Are you sure you want to delete this item? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <ActionButton
            onClick={() => setDeleteIndex(null)}
            sx={{
              backgroundColor: '#E0E0E0',
              color: 'black',
              borderRadius: '24px',
              padding: '6px 20px',
              marginBottom: '20px',
              width: '120px',
              fontSize: '20px',
              fontFamily: 'Montserrat-SemiBold',
              '&:hover': { backgroundColor: '#D0D0D0' },
            }}
          >
            Cancel
          </ActionButton>
          <ActionButton
            onClick={handleDeleteConfirm}
            sx={{
              backgroundColor: '#1A3A6F',
              color: 'white',
              borderRadius: '24px',
              padding: '6px 20px',
              marginBottom: '20px',
              width: '120px',
              fontSize: '20px',
              fontFamily: 'Montserrat-SemiBold',
            }}
            variant='contained'
          >
            Delete
          </ActionButton>
        </DialogActions>
      </Dialog>
      <Dialog open={editIndex !== null} onClose={() => setEditIndex(null)}>
        <Box sx={{ backgroundColor: '#193C6D', color: 'white', padding: '10px' }}>
          <DialogTitle
            sx={{
              display: 'flex',
              justifyContent: 'center',
              fontSize: '30px',
              fontFamily: 'Montserrat-SemiBold',
            }}
          >
            Edit Tag
          </DialogTitle>
        </Box>
        <DialogContent>
          <TextField
            fullWidth
            label='Tag Name'
            value={editTag.name}
            onChange={(e) => setEditTag({ ...editTag, name: e.target.value })}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '45px' } }}
          />

          <FormControlLabel
            control={
              <Checkbox
                checked={editTag.is_shortcut_tag}
                onChange={(e) => setEditTag({ ...editTag, is_shortcut_tag: e.target.checked })}
              />
            }
            label='Shortcut Tag'
          />
        </DialogContent>
        <DialogActions>
          <ActionButton
            onClick={() => setEditIndex(null)}
            sx={{ backgroundColor: '#E0E0E0', borderRadius: '25px', padding: '6px 20px' }}
          >
            Cancel
          </ActionButton>
          <ActionButton
            onClick={handleAddTag}
            sx={{
              backgroundColor: '#1A3A6F',
              color: 'white',
              borderRadius: '25px',
              padding: '6px 20px',
            }}
          >
            Save
          </ActionButton>
        </DialogActions>
      </Dialog>
      <Dialog open={addTagOpen} onClose={() => setAddTagOpen(false)}>
        <Box sx={{ backgroundColor: '#193C6D', color: 'white' }}>
          <DialogTitle
            sx={{
              display: 'flex',
              justifyContent: 'center',
              fontSize: '30px',
              fontFamily: 'Montserrat-SemiBold',
            }}
          >
            Add Tag
          </DialogTitle>
        </Box>
        <DialogContent>
          <TextField
            fullWidth
            label='Tag Name'
            value={newTag.name}
            onChange={(e) => setNewTag({ ...newTag, name: e.target.value })}
            margin='dense'
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '45px' } }}
          />

          <FormControlLabel
            control={
              <Checkbox
                checked={newTag.is_shortcut_tag}
                onChange={(e) => setNewTag({ ...newTag, is_shortcut_tag: e.target.checked })}
              />
            }
            label='Shortcut Tag'
          />
        </DialogContent>
        <DialogActions>
          <ActionButton
            onClick={() => setAddTagOpen(false)}
            sx={{ backgroundColor: '#E0E0E0', borderRadius: '25px', padding: '6px 20px' }}
          >
            Cancel
          </ActionButton>
          <ActionButton
            onClick={handleSave}
            sx={{
              backgroundColor: '#1A3A6F',
              color: 'white',
              borderRadius: '25px',
              padding: '6px 20px',
            }}
          >
            Save
          </ActionButton>
        </DialogActions>
      </Dialog>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    addNewTagOptions: recruitmentEntity.getRecruitment(state).addNewTag,
    editTagOptions: recruitmentEntity.getRecruitment(state).editTag,
  }
}
const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAddNewTag: (data: {}) => dispatch(getNewTag.request({ data })),
    fetchEditTag: (data: {}) => dispatch(editTag.request({ data })),
    fetchDeleteTag: (data: {}) => dispatch(getDeleteTag.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(TagDialog)