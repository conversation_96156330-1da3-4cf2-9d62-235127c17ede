import React from 'react'
import { Box, <PERSON>Field, But<PERSON> } from '@mui/material'
import { SearchBoxCustom, SearchIconStyle } from 'components/Common/CommonStyles'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import {Close as CloseIcon, Search as SearchIcon} from "@mui/icons-material/"

interface SearchBarProps {
  searchTerm: string
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>
  setAddTagOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const SearchBar: React.FC<SearchBarProps> = ({ searchTerm, setSearchTerm, setAddTagOpen }) => {
  return (
    <Box sx={{ display: 'flex' }}>
      <SearchBoxCustom
      id='outlined-basic'
      placeholder='Search Tags'
      variant='outlined'
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      InputProps={{
        startAdornment: <SearchIconStyle />
      }}
      > 
      <CloseIcon />
      </SearchBoxCustom>

      <ActionButton
      onClick={() => setAddTagOpen(true)}
      sx={{
        width:{xs: '10%'}
      }}
      >
        Tags
      </ActionButton>
    </Box>
  )
}

export default SearchBar