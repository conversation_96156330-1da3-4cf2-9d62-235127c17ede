import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Tooltip,
  Card,
  TableSortLabel,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { ExperienceListProps } from './ExperienceTableType'
import Loader from 'components/Common/Loader'
import { StyledTableCell } from 'components/Common/CommonStyles'
import { useState, useEffect } from 'react'

const ExperienceList: React.FC<ExperienceListProps> = ({
  experiences,
  handleEdit,
  handleDelete,
  isExperienceData,
}) => {
  const [orderDirection, setOrderDirection] = useState<'asc' | 'desc'>('asc')
  const [sortedData, setSortedData] = useState(experiences)

  useEffect(() => {
    setSortedData(experiences)
  }, [experiences])

  const handleSort = () => {
    const isAsc = orderDirection === 'asc'
    const sorted = [...experiences].sort((a, b) => {
      const getNumber = (str: string) => {
        const numMatch = str.match(/\d+\.?\d*/)
        return numMatch ? parseFloat(numMatch[0]) : 0
      }

      const hasAdditionalChars = (str: string) => {
        const numMatch = str.match(/\d+\.?\d*/)
        if (!numMatch) return false
        return str.length > numMatch[0].length
      }

      const numA = getNumber(a.experience)
      const numB = getNumber(b.experience)
      const aHasExtra = hasAdditionalChars(a.experience)
      const bHasExtra = hasAdditionalChars(b.experience)

      if (numA !== numB) {
        return isAsc ? numA - numB : numB - numA
      } else if (aHasExtra !== bHasExtra) {
        return isAsc ? (aHasExtra ? 1 : -1) : aHasExtra ? -1 : 1
      } else {
        return isAsc
          ? a.experience.localeCompare(b.experience)
          : b.experience.localeCompare(a.experience)
      }
    })

    setSortedData(sorted)
    setOrderDirection(isAsc ? 'desc' : 'asc')
  }

  return (
    <>
      <Loader state={!isExperienceData} />
      <Card sx={{ borderRadius: '4px', overflow: 'hidden' }}>
        <Table>
          <TableHead sx={{ bgcolor: '#193C6D' }}>
            <TableRow sx={{ height: '50px !important' }}>
              <StyledTableCell
                sx={{
                  fontFamily: 'Montserrat-Semibold',
                  color: 'white',
                  fontSize: '13px !important',
                  width: '50% !important',
                }}
              >
                Experience
                <TableSortLabel
                  active
                  direction={orderDirection}
                  onClick={handleSort}
                  sx={{
                    color: 'white',
                    '& .MuiTableSortLabel-icon': { color: 'white !important' },
                  }}
                ></TableSortLabel>
              </StyledTableCell>

              <StyledTableCell
                sx={{
                  fontFamily: 'Montserrat-Medium',
                  color: 'white',
                  width: '50% !important',
                  fontSize: '13px !important',
                }}
              >
                Actions
              </StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedData.length > 0 ? (
              sortedData.map((exp, index) => (
                <TableRow
                  key={exp.id}
                  sx={{ borderBottom: '1px solid #ddd', height: '50px !important' }}
                >
                  <StyledTableCell
                    sx={{ width: '50% !important', fontFamily: 'Montserrat-Medium' }}
                  >
                    {exp.experience}
                  </StyledTableCell>
                  <StyledTableCell
                    sx={{
                      textAlign: 'right',
                      width: '50% !important',
                      fontFamily: 'Montserrat-Medium',
                      fontSize: '12px !important',
                    }}
                  >
                    <Tooltip title='Edit'>
                      <IconButton sx={{ color: '#193C6D' }} onClick={() => handleEdit(index)}>
                        <EditIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title='Delete'>
                      <IconButton sx={{ color: '#db3700' }} onClick={() => handleDelete(index)}>
                        <DeleteIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                  </StyledTableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={2}
                  align='center'
                  sx={{ fontFamily: 'Montserrat-Medium', color: '#888' }}
                >
                  No data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>
    </>
  )
}

export default ExperienceList
