import { useEffect, useState } from 'react'
import { Box, Card } from '@mui/material'
import ExperienceHeader from './ExperienceHeader'
import ExperienceList from './ExperienceList'
import ExperienceDialog from './ExperienceDialog'
import HandlePagination from '../../HandlePagination'
import ExperienceTitle from './ExperienceTitle'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import {
  addJobExperiences,
  deleteJobExperiences,
  editJobExperiences,
  fetchExperiences,
} from '../../../../../actions'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import DeleteConfirmation from '../../DeleteConfirmation'
import { Experience } from './ExperienceTableType'
import { ExperienceTableProps } from './ExperienceTableType'

const ExperienceTable: React.FC<ExperienceTableProps> = ({
  experienceOptions,
  fetchExperiencesData,
  addExperiencesData,
  editExperiencesData,
  deleteExperiencesData,
  isExperienceData,
}) => {
  const [experiences, setExperiences] = useState<Experience[]>([])
  const [filteredExperiences, setFilteredExperiences] = useState<Experience[]>([])
  const [open, setOpen] = useState<boolean>(false)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [newExperience, setNewExperience] = useState<string>('')
  const [page, setPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const itemsPerPage = 10
  const [experienceExists, setExperienceExists] = useState<boolean>(false)

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }

  useEffect(() => {
    if (searchTerm) {
      const filtered = experiences.filter((exp) =>
        exp.experience.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      setFilteredExperiences(filtered)
    } else {
      setFilteredExperiences(experiences)
    }
  }, [searchTerm, experiences])

  const totalPages = Math.ceil(filteredExperiences.length / itemsPerPage)
  const paginatedExperiences = filteredExperiences.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage,
  )

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleOpen = () => {
    setEditIndex(null)
    setNewExperience('')
    setOpen(true)
  }

  const handleClose = () => setOpen(false)

  const handleEdit = (index: number) => {
    const actualIndex = (page - 1) * itemsPerPage + index
    const experienceToEdit = filteredExperiences[actualIndex]
    setEditIndex(experienceToEdit.id)
    setNewExperience(experienceToEdit.experience)
    setOpen(true)
  }

  const isWithinRange = (value: number, range: string): boolean => {
    const [start, end] = range.match(/[\d.]+/g)?.map(parseFloat) || []
    return start !== undefined && end !== undefined && value >= start && value <= end
  }
  const parseExperienceValue = (value: string): number => {
    const numbers = value.match(/[\d.]+/g)
    if (!numbers) return 0
    if (value.includes('-')) {
      return parseFloat(numbers[0])
    }
    return parseFloat(numbers[0])
  }
  const checkExperienceExists = (experience: string): boolean => {
    const newValue = parseExperienceValue(experience)

    return experiences.some((exp) => {
      const existing = exp.experience.toLowerCase()
      const existingValue = parseExperienceValue(existing)

      if (existing.includes('-')) {
        return isWithinRange(newValue, existing)
      } else {
        return newValue === existingValue
      }
    })
  }

  const handleSaveExperience = (newExperience: string) => {
    if (checkExperienceExists(newExperience)) {
      return
    }

    if (editIndex !== null) {
      editExperiencesData({
        id: editIndex,
        experience: newExperience,
      })

      setExperiences((prev) =>
        [
          ...prev.map((exp) =>
            exp.id === editIndex ? { ...exp, experience: newExperience } : exp,
          ),
        ].sort((a, b) => a.experience.localeCompare(b.experience)),
      )
    } else {
      addExperiencesData({ experience: newExperience })

      setExperiences((prev) =>
        [...prev, { id: Date.now(), experience: newExperience }].sort((a, b) =>
          a.experience.localeCompare(b.experience),
        ),
      )
    }

    setTimeout(() => {
      fetchExperiencesData()
    }, 500)

    handleClose()
  }

  const handleDelete = (index: number) => {
    const actualIndex = (page - 1) * itemsPerPage + index
    setDeleteId(filteredExperiences[actualIndex].id)
  }

  const handleConfirmDelete = () => {
    if (deleteId !== null) {
      deleteExperiencesData(deleteId)
      setTimeout(() => {
        fetchExperiencesData()
      }, 500)
      setDeleteId(null)
    }
  }

  useEffect(() => {
    fetchExperiencesData()
  }, [fetchExperiencesData])

  useEffect(() => {
    if (experienceOptions) {
      const sortedExperiences = [...experienceOptions].sort((a, b) => {
        const numA = parseInt(a.experience.match(/\d+/)?.[0] || '0', 10)
        const numB = parseInt(b.experience.match(/\d+/)?.[0] || '0', 10)
        return numA - numB
      })
      setExperiences(sortedExperiences)
      setFilteredExperiences(sortedExperiences)
    }
  }, [experienceOptions])

  return (
    <Box sx={{ fontFamily: 'Montserrat-Medium', maxWidth: '100%', margin: '10px' }}>
      <Card sx={{ padding: '20px', width: '95%', margin: '20px auto' }}>
        <ExperienceTitle />
        <ExperienceHeader
          handleOpen={handleOpen}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
        />
        <ExperienceList
          experiences={paginatedExperiences}
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          isExperienceData={isExperienceData}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, pr: 2 }}>
          <HandlePagination count={totalPages} page={page} onChange={handlePageChange} />
        </Box>
      </Card>

      <ExperienceDialog
        open={open}
        handleClose={handleClose}
        newExperience={newExperience}
        setNewExperience={setNewExperience}
        handleSaveExperience={handleSaveExperience}
        editIndex={editIndex}
        experienceExists={experienceExists}
        checkExperienceExists={checkExperienceExists}
      />

      <DeleteConfirmation
        open={deleteId !== null}
        handleClose={() => setDeleteId(null)}
        handleConfirm={handleConfirmDelete}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  experienceOptions: recruitmentEntity.getRecruitment(state).getExperienceData,
  isExperienceData: recruitmentStateUI.getRecruitment(state).isExperienceData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchExperiencesData: () => dispatch(fetchExperiences.request()),
  addExperiencesData: (data: { experience: string }) =>
    dispatch(addJobExperiences.request({ data })),
  editExperiencesData: (payload: { id: number; experience: string }) =>
    dispatch(editJobExperiences.request(payload)),
  deleteExperiencesData: (id: number) => dispatch(deleteJobExperiences.request({ id })),
})

export default connect(mapStateToProps, mapDispatchToProps)(ExperienceTable)
