import React, { useState } from 'react';
import { Box, Tabs, Tab, Button, TextField, InputAdornment } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import SearchIcon from '@mui/icons-material/Search';

interface Props {
  searchText: string;
  setSearchText: React.Dispatch<React.SetStateAction<string>>;
}


const PositionHeader: React.FC<Props> = ({ searchText, setSearchText }) => {
  const navigate = useNavigate()


  const handleAddPosition = () => {
    navigate('/home/<USER>/settings/candidate/position/add-position') 
  }

  return (
    <Box>
      <Tabs value={0} sx={{ marginBottom: 2 }}>
        <Tab
          label='Positions'
          sx={{
            backgroundColor: 'rgba(0, 0, 0, 0)',
            fontWeight: '800',
            fontSize: '20px',
            fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
            paddingBottom: 1,
            color: 'rgb(25, 60, 109)',
            '&.Mui-selected': {
              color: 'rgb(25, 60, 109)',
              borderBottom: '2px solid rgb(25, 60, 109)',
            },
            '&:hover': {
              color: 'rgb(25, 60, 109)',
              borderBottom: '2px solid rgb(25, 60, 109)',
            },
            '& .MuiTouchRipple-root': {
              display: 'none',
            },
          }}
        />
      </Tabs>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginTop: 2,
        }}
      >
      
        <Box sx={{ marginRight: 2 }}>
          <TextField
            id='search'
            placeholder='Search'
            variant='outlined'
            size='small'
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              width: '180px',
              '& .MuiOutlinedInput-root': {
                borderRadius: '20px',
              },
            }}
          />
        </Box>

        <Box>
          <Button
            variant='contained'
            onClick={handleAddPosition}
            sx={{
              backgroundColor: '#1f3b60',
              color: '#ffffff',
              borderRadius: '20px',
              '&:hover': { backgroundColor: '#1a2e4a' },
            }}
          >
            Add Position
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default PositionHeader;