import React, { useEffect, useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Pagination,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { useNavigate } from 'react-router-dom'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { deletepositionDetails } from 'actions'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { toast } from 'react-toastify'
 
interface Position {
  id: number
  name: string
  job_description: string
}
 
interface Props {
  positions: Position[]
  deletepositionDetails: (data: { id: number }) => void
  deletePositionDetailsOptions: { message: string }
  resetPositionDetails : ()=>void
}
 
 
const PositionTable: React.FC<Props> = ({
  positions,
  deletepositionDetails,
  deletePositionDetailsOptions,
  resetPositionDetails,
}) => {
  const [page, setPage] = useState(1)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedPositionId, setSelectedPositionId] = useState<number | null>(null)
  const [sortConfig, setSortConfig] = useState<{ key: keyof Position; direction: 'asc' | 'desc' }>({
    key: 'name',
    direction: 'asc',
  })
  const rowsPerPage = 10
  const navigate = useNavigate()
 
  useEffect(() => {
    if (deletePositionDetailsOptions?.message) {
      toast.success(deletePositionDetailsOptions.message)
      resetPositionDetails();
    }
  }, [deletePositionDetailsOptions])
 
  const handleChangePage = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage)
  }
 
  const handleEdit = (position: Position) => {
    navigate('/home/<USER>/settings/candidate/position/add-position', {
      state: { positionData: position },
    })
  }
 
  const handleDeleteClick = (id: number) => {
    setSelectedPositionId(id)
    setDeleteDialogOpen(true)
  }
 
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false)
    setSelectedPositionId(null)
  }
 
  const handleDeleteConfirm = () => {
    if (selectedPositionId !== null) {
      deletepositionDetails({ id: selectedPositionId })
    }
    setDeleteDialogOpen(false)
  }
 
  const handleSort = (key: 'name' | 'job_description') => {
    let direction: 'asc' | 'desc' = 'asc'
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc'
    }
    setSortConfig({ key, direction })
  }
 
  const sortedPositions = [...positions].sort((a, b) => {
    const aValue = a[sortConfig.key]
    const bValue = b[sortConfig.key]
 
    if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1
    if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1
    return 0
  })
 
  const paginatedPositions = sortedPositions.slice((page - 1) * rowsPerPage, page * rowsPerPage)
 
  return (
    <>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'rgb(25, 60, 109)' }}>
              <TableCell sx={{ color: '#fff', fontWeight: 'bold' }}>
                Job Name
                <IconButton onClick={() => handleSort('name')}>
                  {/* <SwapVertIcon sx={{ color: '#fff', opacity:0.25, }} /> */}
                </IconButton>
              </TableCell>
              <TableCell sx={{ color: '#fff', fontWeight: 'bold' }}>
                Job Description
                <IconButton onClick={() => handleSort('job_description')}>
                  {/* <SwapVertIcon sx={{ color: '#fff', opacity:0.25,}} /> */}
                </IconButton>
              </TableCell>
              <TableCell sx={{ color: '#fff', fontWeight: 'bold', textAlign: 'center' }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedPositions.length > 0 ? (
              paginatedPositions.map((position) => (
                <TableRow key={position.id}>
                  <TableCell>{position.name}</TableCell>
                  <TableCell>{position.job_description}</TableCell>
                  <TableCell align='center'>
                    <IconButton color='primary' onClick={() => handleEdit(position)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton color='error' onClick={() => handleDeleteClick(position.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={3} align='center'>
                  No data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
 
      {positions.length > rowsPerPage && (
        <Pagination
          count={Math.ceil(positions.length / rowsPerPage)}
          page={page}
          onChange={handleChangePage}
          color='primary'
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: 2,
          }}
        />
      )}
 
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={handleDeleteDialogClose}
        handleConfirm={handleDeleteConfirm}
        message='Are you sure you want to Delete this Entry?'
      />
    </>
  )
}
 
const mapStateToProps = (state: RootState) => ({
  deletePositionDetailsOptions: recruitmentEntity.getRecruitment(state).deletepositionDetails,
})
 
const mapDispatchToProps = (dispatch: Dispatch) => ({
  deletepositionDetails: (data: any) => dispatch(deletepositionDetails.request({ data })),
  resetPositionDetails: () => dispatch(deletepositionDetails.reset()),
})
 
export default connect(mapStateToProps, mapDispatchToProps)(PositionTable)
 