import React, { useEffect, useState } from 'react'
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Card,
  TextField,
  InputAdornment,
  TableSortLabel,
} from '@mui/material'
import QualificationModal from './QualificationModal'
import ActionCell from '../../ActionCell'
import CustomPagination from '../../CustomPagination'
import {
  addManageQualification,
  deleteQualification,
  editQualification,
  fetchAddQualification,
} from '../../../../../actions'
import { Dispatch } from 'redux'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import { RootState } from '../../../../../configureStore'
import { connect } from 'react-redux'
import { toast } from 'react-toastify'
import SearchIcon from '@mui/icons-material/Search';
import Loader from 'components/Common/Loader'
function QualificationScreen(props: any) {
  const [openModal, setOpenModal] = useState(false)
  const [qualifications, setQualifications] = useState<{ id: number; qualification: string }[]>([])
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [currentQualification, setCurrentQualification] = useState<string>('')
  const [page, setPage] = useState(1)
  const itemsPerPage = 10
  const [isClicked, setIsClicked] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')
  const {
    fetchAddQualification,
    AddQualificationOptions,
    addManageQualification,
    isgetAddQualificationData,
    deleteQualification,
  } = props
  const filteredQualifications = qualifications.filter((qualification) =>
    qualification.qualification.toLowerCase().includes(searchTerm.toLowerCase()),
  )
  if (deleteQualification) {
  }

  const paginatedUnsorted = filteredQualifications.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage,
  )

  const paginatedQualification = [...paginatedUnsorted].sort((a, b) =>
    order === 'asc'
      ? a.qualification.localeCompare(b.qualification)
      : b.qualification.localeCompare(a.qualification),
  )

  const totalPages = Math.ceil(filteredQualifications.length / itemsPerPage)
  const handleClick = () => {
    setIsClicked(true)
    setTimeout(() => setIsClicked(false), 200)
  }
  const handleOpenModal = (qualification?: string, id?: number) => {
    if (qualification !== undefined && id !== undefined) {
      setCurrentQualification(qualification)
      setEditIndex(id)
    } else {
      setCurrentQualification('')
      setEditIndex(null)
    }
    setOpenModal(true)
  }
  const handleCloseModal = () => {
    setOpenModal(false)
    setEditIndex(null)
    setCurrentQualification('')
  }
  const handleSaveQualification = (newQualification: string) => {
    if (editIndex !== null) {
      const updatedQualification = { id: editIndex, qualification: newQualification }
      setQualifications((prev) =>
        prev.map((q) => (q.id === updatedQualification.id ? updatedQualification : q)),
      )
      props.editQualification(updatedQualification)
      toast.success('Qualification Edited Successfully')
      setTimeout(() => {
        props.fetchAddQualification()
      }, 500)
    } else {
      const newEntry = { qualification: newQualification }
      props.addManageQualification(newEntry)
      toast.success('Qualification Added Successfully')
      setTimeout(() => {
        props.fetchAddQualification()
      }, 500)
    }
    handleCloseModal()
  }
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }
  const handleDeleteQualification = (id: number) => {
    setQualifications((prev) => prev.filter((q) => q.id !== id))
    props.deleteQualification(id)
    toast.success('Qualification Deleted Successfully')
    setTimeout(() => {
      props.fetchAddQualification()
    }, 500)
  }
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }
  useEffect(() => {
    fetchAddQualification()
  }, [])
  useEffect(() => {
    if (Array.isArray(AddQualificationOptions)) {
      const formattedData = AddQualificationOptions.flat().map((item: any) => ({
        id: item.id,
        qualification: item.qualification,
      }))
      setQualifications(formattedData)
    }
  }, [AddQualificationOptions])

  return (
    <Box sx={{ p: 3, fontFamily: 'Montserrat-Medium', maxWidth: '100%' }}>
      <Card
        sx={{
          padding: '20px',
          width: '100%',
          justifyContent: 'center',
          margin: 'auto',
          maxWidth: '98%',
        }}
      >
        <Typography
          variant='h6'
          sx={{
            fontFamily: 'Montserrat-Semibold',
            fontWeight: 600,
            position: 'relative',
            display: 'inline-block',
            padding: '8px 16px',
            color: 'primary.main',
            borderRadius: '4px',
            cursor: 'pointer',
            transition: 'background-color 0.3s ease-in-out, color 0.3s ease-in-out',
            backgroundColor: isClicked ? 'rgba(25, 60, 109, 0.3)' : 'transparent',
            '&::after': {
              content: '""',
              display: 'block',
              width: '100%',
              height: '2px',
              backgroundColor: '#193C6D',
              position: 'absolute',
              bottom: '0px',
              left: 0,
            },
          }}
          onClick={handleClick}
        >
          Qualifications
        </Typography>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 0,
            gap: 2,
          }}
        >
          <Box sx={{ flex: 1 }}>
            <TextField
              variant="outlined"
              placeholder="Search Qualification"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
                sx: {
                  borderRadius: '50px',
                  height: '40px',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '50px',
                  },
                },
              }}
              sx={{
                flex: 1,
                minWidth: '230px',
                maxWidth: '18%',
                '& .MuiInputBase-input': {
                  fontFamily: 'Montserrat-Medium',
                },
              }}
            />
          </Box>
          <Box>
            <Button
              variant='contained'
              color='secondary'
              onClick={() => handleOpenModal()}
              sx={{
                fontFamily: 'Montserrat-Semibold',
                borderRadius: '24px',
                fontSize: '16px',
                paddingLeft: '28px',
                paddingRight: '22px',
                fontWeight: '700',
              }}
            >
              Add Qualification
            </Button>
          </Box>
        </Box>
        <Loader state={!isgetAddQualificationData} />
        <Card sx={{ borderRadius: '4px' }}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead sx={{ bgcolor: '#193C6D' }}>
                <TableRow>
                  <TableCell
                    sx={{
                      fontFamily: 'Montserrat-Medium',
                      fontWeight: 600,
                      color: 'white',
                      textAlign: 'center',
                      fontSize: '15px',
                      width: '50%',
                    }}
                  >
                    <TableSortLabel
                      active={true}
                      direction={order}
                      onClick={() => setOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'))}
                      sx={{
                        color: 'white',
                        '& .MuiTableSortLabel-icon': {
                          color: 'white !important',
                        },
                      }}
                    >
                      <span style={{ color: 'white', fontWeight: 600 }}>Qualifications</span>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell
                    align='right'
                    sx={{
                      fontFamily: 'Montserrat-Medium',
                      fontWeight: 600,
                      color: 'white',
                      textAlign: 'center',
                      fontSize: '15px',
                      width: '50%',
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedQualification.length > 0 ? (
                  paginatedQualification.map((qualification, index) => (
                    <TableRow key={index} sx={{ borderBottom: '1px solid #ddd' }}>
                      <TableCell
                        sx={{
                          fontFamily: 'Montserrat-Medium',
                          fontWeight: 600,
                          textAlign: 'center',
                        }}
                      >
                        {qualification.qualification}
                      </TableCell>
                      <TableCell
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          textAlign: 'center',
                        }}
                      >
                        <ActionCell
                          onEdit={() =>
                            handleOpenModal(qualification.qualification, qualification.id)
                          }
                          onDelete={() => handleDeleteQualification(qualification.id)}
                        />
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={2}
                      align='center'
                      sx={{
                        fontFamily: 'Montserrat-Medium',
                        fontWeight: 600,
                        py: 3,
                        color: 'black',
                      }}
                    >
                      No records found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, pr: 2 }}>
          <CustomPagination count={totalPages} page={page} onChange={handlePageChange} />
        </Box>
      </Card>
      <QualificationModal
        open={openModal}
        onClose={handleCloseModal}
        onAdd={handleSaveQualification}
        initialData={
          editIndex !== null
            ? qualifications.find((q) => q.id === editIndex)?.qualification
            : undefined
        }
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    AddQualificationOptions: recruitmentEntity.getRecruitment(state).getAddQualificationData,
    isgetAddQualificationData: recruitmentStateUI.getRecruitment(state).isgetAddQualificationData,
    deleteQualification: recruitmentEntity.getRecruitment(state).deleteQualification,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchAddQualification: () => dispatch(fetchAddQualification.request()),
    addManageQualification: (data: any) => dispatch(addManageQualification.request({ data })),
    deleteQualification: (id: number) => dispatch(deleteQualification.request({ id })),
    editQualification: (data: { id: number; qualification: string }) =>
      dispatch(editQualification.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(QualificationScreen)
