import React from 'react'
import { Box, Button, TextField, InputAdornment, IconButton } from '@mui/material'
import FilterListIcon from '@mui/icons-material/FilterList'
import SearchIcon from '@mui/icons-material/Search'
import ClearIcon from '@mui/icons-material/Clear'

interface ActionButtonsComponentProps {
  onFilter: () => void
  onAddInstitute: () => void
  onSendTestEmail: () => void
  onSendEmail: () => void
  searchValue: string
  setSearchValue: (value: string) => void
}

const ActionButtonsComponent: React.FC<ActionButtonsComponentProps> = ({
  onFilter,
  onAddInstitute,
  onSendTestEmail,
  onSendEmail,
  searchValue,
  setSearchValue,
}) => {
  const handleClearSearch = () => {}

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '4px',
        marginTop: '-8px',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          backgroundColor: 'white',
          borderRadius: '20px',
          padding: '4px',
          maxWidth: '300px',
          flexGrow: 1,
        }}
      >
        <TextField
          id='outlined-basic'
          placeholder='Search Institute'
          variant='outlined'
          size='small'
          fullWidth
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position='start'>
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: searchValue && (
              <InputAdornment position='end'>
                <IconButton onClick={handleClearSearch}>
                  <ClearIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '20px',
            },
          }}
        />
      </Box>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px',
        }}
      >
        <Button
          variant='contained'
          onClick={onAddInstitute}
          sx={{
            backgroundColor: 'rgb(25, 60, 109)',
            borderRadius: '20px',
            fontSize: '13px',
            fontWeight: 'normal',
            height: '42px',
            float: 'right',
            marginTop: '15px',
            padding: '5px 20px',
            '&:hover': { backgroundColor: 'rgb(20, 50, 90)' },
          }}
        >
          Add Institute
        </Button>

        <Button
          variant='contained'
          onClick={onSendTestEmail}
          sx={{
            backgroundColor: 'rgb(25, 60, 109)',
            borderRadius: '20px',
            fontSize: '13px',
            paddingBottom: '8px',
            paddingTop: '8px',
            paddingRight: '20px',
            paddingLeft: '20px',
            fontWeight: 'normal',
            height: '42px',
            float: 'right',
            marginTop: '15px',
            padding: '5px 20px',
            '&:hover': { backgroundColor: 'rgb(20, 50, 90)' },
          }}
        >
          Send Test Email
        </Button>

        <Button
          variant='contained'
          onClick={onSendEmail}
          sx={{
            backgroundColor: 'rgb(25, 60, 109)',
            borderRadius: '20px',
            fontSize: '13px',
            paddingBottom: '8px',
            paddingTop: '8px',
            paddingRight: '20px',
            paddingLeft: '20px',
            fontWeight: 'normal',
            height: '42px',
            float: 'right',
            marginTop: '15px',
            padding: '5px 20px',
            '&:hover': { backgroundColor: 'rgb(20, 50, 90)' },
          }}
        >
          Send Email
        </Button>
      </Box>
    </Box>
  )
}

export default ActionButtonsComponent
