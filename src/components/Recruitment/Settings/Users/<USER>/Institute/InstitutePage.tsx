import React, { useEffect, useState } from 'react'
import InstituteTable from './InstituteTableComponent'
import { Box, Tabs, Tab, Pagination } from '@mui/material'
import FilterComponent from './FilterComponent'
import ActionButtonsComponent from './ActionButtonsComponent'
import { useNavigate, useParams } from 'react-router-dom'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import { addInstituteDetails, editInstDetails, fetchTpoDetails, delInstDetails } from 'actions'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'

const TPOContent: React.FC = () => <div>TPO Content</div>
const ConsultanciesContent: React.FC = () => <div>Consultancies Content</div>
const Colleges: React.FC = () => <div>Colleges Content</div>

function InstitutePage(props: any) {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState<number>(1)
  const { getInstituteDetails, editInstDetails, InstituteDetailsOptions, delInstDetails } = props

  interface InstituteData {
    id: number
    instituteName: string
    type: string
    contactPerson: string
    email: string
    website: string
    phoneNumber: string
    lastTouch: string
    fresherCharges: string
    experienceCharges: string
    status: string
  }

  const [data, setData] = useState<InstituteData[]>([])
  const [searchValue, setSearchValue] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const entriesPerPage = 10
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedId, setSelectedId] = useState<number | null>(null)

  useEffect(() => {
    getInstituteDetails({ representative_type: 'Institute' })
  }, [])

  useEffect(() => {
    if (InstituteDetailsOptions?.length > 0) {
      const formattedData = InstituteDetailsOptions.map((item: any) => ({
        id: item.id,
        instituteName: item.college_name,
        type: item.representative_type,
        contactPerson: item.name,
        email: item.email,
        website: item.website,
        phoneNumber: item.phone_no,
        lastTouch: item.last_touch,
        fresherCharges: item.fresher_charges,
        experienceCharges: item.experience_charges,
        status: item.status,
      }))
      setData(formattedData)
    }
  }, [InstituteDetailsOptions])

  const filteredData = data.filter(
    (item) =>
      item.instituteName.toLowerCase().includes(searchValue.toLowerCase()) ||
      item.contactPerson.toLowerCase().includes(searchValue.toLowerCase()) ||
      item.email.toLowerCase().includes(searchValue.toLowerCase()) ||
      item.phoneNumber.includes(searchValue),
  )
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page)
  }

  const paginatedData = filteredData.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage,
  )

  const handleEdit = (id: number) => {
    editInstDetails({ id: id })
    navigate(`/home/<USER>/settings/users/contacts/institute/edit/${id}`)
  }

  const handleDelete = (id: number) => {
    setSelectedId(id)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = () => {
    if (selectedId !== null) {
      delInstDetails({ id: selectedId })
    }
    setDeleteDialogOpen(false)
    setSelectedId(null)
  }

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false)
    setSelectedId(null)
  }
  const handleAddInstitute = () => {
    navigate('/home/<USER>/settings/users/contacts/institute/add-institute')
  }

  const handleSendTestEmail = () => {}

  const handleSendEmail = () => {}

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const renderContent = () => {
    switch (activeTab) {
      case 0:
        return <TPOContent />
      case 1:
        return (
          <>
            <ActionButtonsComponent
              onAddInstitute={handleAddInstitute}
              onSendTestEmail={handleSendTestEmail}
              onSendEmail={handleSendEmail}
              onFilter={() => {}}
              searchValue={searchValue}
              setSearchValue={setSearchValue}
            />
            <InstituteTable
              data={paginatedData}
              onEdit={(id: number) => handleEdit(id)}
              onDelete={handleDelete}
            />
            <Pagination
              count={Math.ceil(data.length / entriesPerPage)}
              page={currentPage}
              onChange={handlePageChange}
              color='primary'
              sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}
            />
          </>
        )
      case 2:
        return <ConsultanciesContent />
      case 3:
        return <Colleges />
      default:
        return null
    }
  }
  return (
    <Box
      sx={{
        width: '100%',
        margin: '10px auto 70px auto',
        backgroundColor: 'white',
        paddingTop: { lg: '0.1rem' },
        borderRadius: { sm: '6px', lg: '4px' },
        opacity: 1,
      }}
    >
      {renderContent()}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={handleDeleteDialogClose}
        handleConfirm={handleDeleteConfirm}
        message='Are you sure you want to Delete this Entry?'
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  InstituteDetailsOptions: recruitmentEntity.getRecruitment(state).getInstituteDetails,
  editInstituteDetailsOptions: recruitmentEntity.getRecruitment(state).EditInstDetails || {},
  deleteInstituteDetailsOptions: recruitmentEntity.getRecruitment(state).DelInstDetails || {},
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  getInstituteDetails: (data: any) => dispatch(addInstituteDetails.request({ data })),
  editInstDetails: (data: any) => dispatch(editInstDetails.request({ data })),
  delInstDetails: (data: any) => dispatch(delInstDetails.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(InstitutePage)
