import React from "react";
import { Box, Tabs, Tab } from "@mui/material";

const TopHeading: React.FC = () => {
  return (
    <Box sx={{ display: "flex", alignItems: "center" }}>
      <Tabs value={0} aria-label="Tabs for different tables" sx={{ display: "flex", flexDirection: "row" }}>
        <Tab
          label="Interview Data" 
          sx={{
            backgroundColor: "rgba(0, 0, 0, 0)",
            fontWeight: "800",
            fontSize: "medium",
            fontFamily: 'Montserrat-Medium',
            paddingBottom: 1,
            color: "rgb(25, 60, 109)", 
            "&.Mui-selected": {
              color: "rgb(25, 60, 109)", 
              borderBottom: "2px solid rgb(25, 60, 109)", 
            },
            "&:hover": {
              color: "rgb(25, 60, 109)", 
              borderBottom: "2px solid rgb(25, 60, 109)",
            },
            "& .<PERSON>iTouchRipple-root": {
              display: "none",
            },
          }}
        />
      </Tabs>
    </Box>
  );
};

export default TopHeading;