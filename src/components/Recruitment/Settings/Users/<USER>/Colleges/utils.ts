import { useState, useMemo, useCallback, ChangeEvent, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CollegeDriveData, CollegesPayload, OrgData } from '../../../../../Types';
import dayjs from 'dayjs';
import { handleToasts } from 'components/Recruitment/Common/notifications';

export interface College {
    id: number;
    organisation: string;
    city: string;
    created_at: string;
    updated_at: string;
    last_invited: string;
    representative_type: string;
    last_drive_date: string | null;
    notes: string;
}

export interface CollegeTableProps {
    data: College[];
    deleteCollege: (data: { id: number }) => void;
    getColleges: (data: CollegesPayload) => void;
    isCollegeDeleted: boolean;
}

export const columnMappings: Record<string, string> = {
    organisation: 'College Name',
    city: 'City',
    last_drive_date: 'Last Drive Date'
};

export const processAPIResponse = (response: College[] | { data?: College[] }): College[] => {
    let dataArray: College[];
    if (Array.isArray(response)) {
        const firstElement = response[0];
        if (Array.isArray(firstElement)) {
            dataArray = firstElement;
        } else {
            dataArray = response;
        }
    } else {
        dataArray = response.data ?? [];
    }
    return dataArray.map(row => ({
        ...row,
        last_drive_date: row.last_drive_date
            ? dayjs(row.last_drive_date).format('MM-DD-YYYY')
            : 'N/A'
    }));
};

export const useCollegeTableLogic = (
    data: College[],
    deleteCollege: (data: { id: number }) => void,
    isCollegeDeleted: boolean
) => {
    type Order = 'asc' | 'desc';
    const [order, setOrder] = useState<Order>('asc');
    const [orderBy, setOrderBy] = useState<keyof College>('organisation');
    const [selectedRow, setSelectedRow] = useState<College | null>(null);
    const [selectedEditRow, setSelectedEditRow] = useState<College | null>(null);
    const displayColumns: (keyof Pick<College, 'organisation' | 'city' | 'last_drive_date'>)[] = [
        'organisation',
        'city',
        'last_drive_date'
    ];
    const handleDeleteClick = useCallback((row: College) => {
        setSelectedRow(row);
    }, []);
    const handleConfirmDelete = () => {
        if (selectedRow) {
            deleteCollege({ id: selectedRow.id });
        }
    };
    const handleSort = useCallback(
        (column: keyof College) => {
            const isAsc = orderBy === column && order === 'asc';
            setOrder(isAsc ? 'desc' : 'asc');
            setOrderBy(column);
        },
        [order, orderBy]
    );
    const handleEditClick = useCallback((row: College) => {
        setSelectedEditRow(row);
    }, []);
    const sortedData = useMemo(() => {
        return [...data].sort((a, b) => {
            const aValue = (a[orderBy] ?? '').toString().trim().toLowerCase();
            const bValue = (b[orderBy] ?? '').toString().trim().toLowerCase();
            if (aValue < bValue) return order === 'asc' ? -1 : 1;
            if (aValue > bValue) return order === 'asc' ? 1 : -1;
            return 0;
        });
    }, [data, order, orderBy]);
    return {
        displayColumns,
        order,
        orderBy,
        selectedRow,
        handleDeleteClick,
        handleConfirmDelete,
        handleSort,
        sortedData,
        handleEditClick,
        selectedEditRow,
        setSelectedEditRow
    };
};

export interface CollegesProps {
    getColleges: (data: CollegesPayload) => { type: string };
    deleteCollege: (data: { id: number }) => void;
    addDriveDetails: (data: CollegeDriveData) => void;
    EditOrgDetails: (data: OrgData) => void;
    resetCollegeDeleted: () => {};
    resetDriveAdded: () => {};
    resetOrgEdited: () => {};
    collegeData: College[];
    isGetCollegeData: boolean;
    isCollegeDeleted: boolean;
    isCollegeDeleting: boolean;
    isDriveAdded: boolean;
    isOrgEdited: boolean;
    isDriveAdding: boolean;
    isOrgEditing: boolean;
}

export const useCollegesLogic = (props: CollegesProps) => {
    const { collegeData } = props;
    const [page, setPage] = useState<number>(1);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const rowsPerPage = 10;
    const navigate = useNavigate();
    const processedData = useMemo(() => processAPIResponse(collegeData), [collegeData]);
    const handleSearchChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
        setPage(1);
    }, []);
    const clearSearch = useCallback(() => {
        setSearchQuery('');
        setPage(1);
    }, []);
    const handlePageChange = useCallback((event: ChangeEvent<unknown>, value: number) => {
        event.preventDefault();
        setPage(value);
    }, []);

    const currentData = useMemo(() => {
        return processedData.slice((page - 1) * rowsPerPage, page * rowsPerPage);
    }, [processedData, page, rowsPerPage]);
    return {
        page,
        searchQuery,
        rowsPerPage,
        handleSearchChange,
        clearSearch,
        handlePageChange,
        currentData,
        processedData,
        navigate
    };
};

export interface useDriveInterface {
    addDriveDetails: (data: CollegeDriveData) => void;
}

export const useDriveLogic = (props: useDriveInterface) => {
    const { addDriveDetails } = props;
    const [selectedDriveCollege, setSelectedDriveCollege] = useState<{
        id: number;
        organisation: string;
        city: string;
    } | null>(null);
    const handleDriveSubmit = (payload: CollegeDriveData) => {
        addDriveDetails(payload);
    };
    const handleDriveAddClick = () => { };
    return { selectedDriveCollege, setSelectedDriveCollege, handleDriveSubmit, handleDriveAddClick };
};

export const useCollegeSideEffects = ({
    getColleges,
    page,
    rowsPerPage,
    searchQuery,
    isCollegeDeleted,
    isDriveAdded,
    isOrgEdited,
    resetDriveAdded,
    resetOrgEdited,
    resetCollegeDeleted,
    toast,
    setSelectedDriveCollege,
    selectedDriveCollege,
    onOpen,
    isOpen,
    addDriveButtonRef
}: {
    getColleges: (data: CollegesPayload) => void;
    page: number;
    rowsPerPage: number;
    searchQuery: string;
    isCollegeDeleted: boolean;
    isDriveAdded: boolean;
    isOrgEdited: boolean;
    resetDriveAdded: () => void;
    resetOrgEdited: () => void;
    resetCollegeDeleted: () => void;
    toast: any;
    setSelectedDriveCollege: (data: any) => void;
    selectedDriveCollege: any;
    onOpen: () => void;
    isOpen: boolean;
    addDriveButtonRef: React.RefObject<HTMLButtonElement>;
}) => {

    useEffect(() => {
        if (isCollegeDeleted || isDriveAdded || isOrgEdited) {
            getColleges({ representative_type: 'College' });
        }
    }, [isCollegeDeleted, isDriveAdded, isOrgEdited]);

    useEffect(() => {
        if (searchQuery === "") {
            getColleges({ representative_type: 'College' });
            return;
        }
        const timer = setTimeout(() => {
            getColleges({
                representative_type: 'College',
                pageNumber: page,
                limit: rowsPerPage,
                search_input: searchQuery,
            });
        }, 500);
        return () => clearTimeout(timer);
    }, [searchQuery, page, rowsPerPage]);

    useEffect(() => {
        handleToasts([
            { condition: isDriveAdded, message: 'Drive added successfully', reset: resetDriveAdded },
            { condition: isOrgEdited, message: 'College edited successfully', reset: resetOrgEdited },
            { condition: isCollegeDeleted, message: 'College deleted successfully', reset: resetCollegeDeleted },
        ]);
    }, [isDriveAdded, isOrgEdited, isCollegeDeleted, resetDriveAdded, resetOrgEdited, resetCollegeDeleted]);

    const handleRowClick = (row: any) => {
        setSelectedDriveCollege(row);
    };
    const handleAddDriveClick = () => {
        if (selectedDriveCollege) {
            onOpen();
        }
    };
    const handleClickAway = (event: MouseEvent | TouchEvent) => {
        if (isOpen) return;
        if (addDriveButtonRef.current && addDriveButtonRef.current.contains(event.target as Node)) {
            return;
        }
        setSelectedDriveCollege(null);
    };
    return { handleRowClick, handleAddDriveClick, handleClickAway };
};
