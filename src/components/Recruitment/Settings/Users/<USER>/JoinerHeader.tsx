import React, { useState, useEffect } from 'react'
import { Box, Paper, Stack, Tab, Tabs, Modal } from '@mui/material'
import FilterListIcon from '@mui/icons-material/FilterList'
import AddJoinerPage from './AddJoinerPage'
import { ActionButton } from '../../../../HolidayList/HolidaysStyles'
import { SearchBoxCustom } from '../../../../Common/CommonStyles'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity } from '../../../../../reducers'
import { Dispatch } from 'redux'
import DebouncedSearchedBox from 'components/Common/DebouncedSearchBox'
import { addExpectedJoiners, fetchBatches, fetchRoundsByType } from '../../../../../actions'
import connect from 'react-redux/es/components/connect'
import JoinerDetails from './JoinerDetails'
import AddEditJoiner from './AddEditJoiner'
import useOpenable from 'hooks/useOpenable'
import { toast } from 'react-toastify'
import JoinersDrop from './JoinersDrop'
import { useJoinerContext } from './JoinerContext'
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import { TextField, InputAdornment, IconButton } from '@mui/material';


interface JoinerHeaderProps {
  searchQuery: string
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>
  batchDropDown: any[]
  fetchBatchDropdown: () => void
  RoundOptions: any[]
  fetchRoundsByType: (data: {}) => void
  addExpectedJoiners: (data: {}) => void
}

const JoinerHeader: React.FC<JoinerHeaderProps> = ({
  searchQuery,
  setSearchQuery,
  batchDropDown,
  fetchBatchDropdown,
  RoundOptions,
  fetchRoundsByType,
  addExpectedJoiners,
}) => {
  const [editData, setEditData] = useState<any | null>(null)
  const { isOpen, onOpen, onClose } = useOpenable()
  const { isOpen: isFilterOpen, onOpenChange } = useOpenable()
  const {state} = useJoinerContext()
  useEffect(() => {
    fetchBatchDropdown()
  }, [fetchBatchDropdown])

  useEffect(() => {
    fetchRoundsByType({ round_type: 2 })
  }, [fetchRoundsByType])
  
  console.log(batchDropDown)
  const batchArray = batchDropDown.map((item) =>({id:item.id,name:item.batch}))

  const roundsArray = RoundOptions.map((item) => ({id:item.id,name:item.round_name}))

  return (
    <>
      <Paper
        sx={{
          position: 'sticky',
          top: 0,
          zIndex: 100,
          p: 2,
          boxShadow: 3,
          backgroundColor: '#fff',
        }}
      >
        <Box className='templates-page-tabs'>
          <Tabs value={0} aria-label='Tabs for different tables'>
            <Tab sx={{ fontSize: '20px' , fontFamily: "Montserrat-Semibold",
              fontWeight: 600}} label='Expected Joiners' />
          </Tabs>
        </Box>

        <Box display={'flex'} alignItems={'center'} justifyContent={'flex-start'} my={'15px'}>
        <Box width='calc(50% - 100px)'>
  <TextField
    id='outlined-basic'
    placeholder='Search Joiners'
    variant='outlined'
    size='small'
    value={searchQuery}
    onChange={(e) => setSearchQuery(e.target.value)}
    sx={{
      width: '250px',
      borderRadius: '25px', // Keeps the search bar rounded
      '& .MuiOutlinedInput-root': {
        borderRadius: '25px', // Ensures the input field remains rounded
      },
    }}
    style={{ width: '250px', margin: '0' }}
    InputProps={{
      startAdornment: (
        <InputAdornment position="start">
          <SearchIcon />
        </InputAdornment>
      ),
      endAdornment: searchQuery && (
        <InputAdornment position="end">
          <IconButton onClick={() => setSearchQuery('')} size="small">
            <CloseIcon />
          </IconButton>
        </InputAdornment>
      ),
    }}
  />
</Box>

          <Stack direction='row' spacing={2} sx={{ ml: 'auto' }}>
            <ActionButton
              variant='contained'
              color='primary'
              onClick={onOpenChange}
              startIcon={<FilterListIcon />}
            >
              Filters
            </ActionButton>

            <ActionButton
  sx={{
    marginTop: 0,
    '&.Mui-disabled': {
      color: 'white',
      cursor: 'not-allowed',
      pointerEvents: 'auto',
      bgcolor: 'grey',
    },
  }}
  disabled={state.roundFilter ? false : true}
  onClick={() => toast.error("Email can't be sent!")} 
>
  Send Email
</ActionButton>

<ActionButton
  sx={{
    marginTop: 0,
    '&.Mui-disabled': {
      color: 'white',
      cursor: 'not-allowed',
      pointerEvents: 'auto',
      bgcolor: 'grey',
    },
  }}
  disabled={state.roundFilter ? false : true}
  onClick={() => toast.error("Test Mail can't be sent!")} 
>
  Send Test Mail
</ActionButton>


            <ActionButton
              variant='contained'
              color='primary'
              onClick={() => {
                setEditData(null)
                onOpen()
              }}
            >
              Add Expected Joiner
            </ActionButton>
          </Stack>
        </Box>
        <Box sx={{ marginTop: 2 }}>
          <JoinersDrop
            open={isFilterOpen}
            batchDropDown={batchArray}
            RoundOptions={roundsArray}
          />
        </Box>
      </Paper>
      <JoinerDetails
        searchQuery={searchQuery}
        onEdit={(data) => {
          setEditData(data)
          onOpen()
        }}
      />
      <AddEditJoiner isOpen={isOpen} onClose={onClose} editData={editData} />
    </>
  )
}

const mapStateToProps = (state: RootState) => ({
  batchDropDown: recruitmentEntity.getRecruitment(state).getBatchesData,
  RoundOptions: recruitmentEntity.getRecruitment(state).getRoundsByTypeData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchBatchDropdown: () => dispatch(fetchBatches.request()),
  fetchRoundsByType: (data: {}) => dispatch(fetchRoundsByType.request({ data })),
  addExpectedJoiners: (data: {}) => dispatch(addExpectedJoiners.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(JoinerHeader)
