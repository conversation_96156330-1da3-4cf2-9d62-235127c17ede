import React, { useEffect } from 'react'
import { Box, Button } from '@mui/material'
import SearchInput from './SearchInput'
import { useNavigate } from 'react-router-dom'

interface InterviewerHeaderProps {
  searchTerm: string
  setSearchTerm: (value: string) => void
}

const InterviewerHeader: React.FC<InterviewerHeaderProps> = ({ searchTerm, setSearchTerm }) => {
  const navigate = useNavigate()

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0px 10px',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '270px' }}>
        <SearchInput label='Search by Name' value={searchTerm} onChange={setSearchTerm} />
      </Box>

      <Button
        sx={{
          borderRadius: '35px',
          fontSize: 'medium',
          fontFamily: 'Montserrat-Medium',
        }}
        variant='contained'
        onClick={() => navigate('/home/<USER>/settings/users/interviewer/addinterviewer')}
      >
        Add Interviewer
      </Button>
    </Box>
  )
}

export default InterviewerHeader
