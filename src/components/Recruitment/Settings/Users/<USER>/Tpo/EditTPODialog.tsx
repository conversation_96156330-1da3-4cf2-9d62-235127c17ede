import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>Title, DialogContent, <PERSON>alogActions, <PERSON><PERSON>ield, But<PERSON> } from '@mui/material'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { Edit_Tpo } from 'actions'
import { recruitmentStateUI } from 'reducers'
import ActionBar from 'components/ProjectManagement/ProjectResourceReport/ActionBar'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { toast } from 'react-toastify'

interface EditTPODialogProps {
  open: boolean
  onClose: () => void
  tpo: {
    id: number
    name: string
    type: string
    email: string
    college_name: string
    lastTouch: string
    phone_no: string
    status: string
  } | null
  fetchEditTpo: (updatedTPO: any) => void
}

const EditTPODialog: React.FC<EditTPODialogProps> = ({ open, onClose, tpo, fetchEditTpo }) => {
  const initialState = {
    id: 0,
    name: '',
    type: '',
    email: '',
    college_name: '',
    lastTouch: '',
    phone_no: '',
    status: '',
  }

  const [editedTPO, setEditedTPO] = useState(tpo || initialState)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isFormValid, setIsFormValid] = useState(false)
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  useEffect(() => {
    setEditedTPO(tpo || initialState)
  }, [tpo])

  useEffect(() => {
    validateForm()
  }, [editedTPO])

  const validateForm = () => {
    if (!editedTPO) return

    const newErrors = {
      name: editedTPO.name?.trim() ? '' : 'Contact Person is required',
      email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editedTPO.email || '') ? '' : 'Invalid email format',
      college_name: editedTPO.college_name?.trim() ? '' : 'College is required',
      phone_no: /^\d{10}$/.test(editedTPO.phone_no || '') ? '' : 'Phone must be 10 digits',
      status: editedTPO.status?.trim() ? '' : 'Status is required',
    }

    setErrors(newErrors)
    setIsFormValid(Object.values(newErrors).every((error) => error === ''))
  }

  const handleChange = (field: keyof typeof editedTPO, value: string) => {
    setEditedTPO((prev) => ({
      ...prev!,
      [field]: value || '',
    }))
  }

  const handleBlur = (field: keyof typeof editedTPO) => {
    setTouched((prev) => ({
      ...prev,
      [field]: true,
    }))
  }

  const handleSave = () => {
    const formattedTPO = {
      name: editedTPO.name,
      email: editedTPO.email,
      phone_no: editedTPO.phone_no,
      status: editedTPO.status,
      id: String(editedTPO.id),
      stage: 25,
      notes: '<ul>\n\t<li>Test</li>\n</ul>\n',
      college_name: editedTPO.college_name,
      representative_type: 'TPO',
      last_touch: editedTPO.lastTouch || '',
    }

    fetchEditTpo(formattedTPO)
    onClose()
    toast.success('Tpo Edited Successfully!')
  }

  if (!editedTPO) return null

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
      <DialogTitle
        sx={{ textAlign: 'center', backgroundColor: '#193C6D', color: 'white', mb: '15px' }}
      >
        Edit TPO
      </DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label='Contact Person'
          value={editedTPO.name}
          onChange={(e) => handleChange('name', e.target.value)}
          onBlur={() => handleBlur('name')}
          margin='dense'
          error={touched.name && !!errors.name}
          helperText={touched.name ? errors.name : ''}
          InputProps={{
            sx: { borderRadius: '25px', mt: '9px' },
          }}
        />
        <TextField
          fullWidth
          label='Email'
          value={editedTPO.email}
          onChange={(e) => handleChange('email', e.target.value)}
          onBlur={() => handleBlur('email')}
          margin='dense'
          error={touched.email && !!errors.email}
          helperText={touched.email ? errors.email : ''}
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='College'
          value={editedTPO.college_name}
          onChange={(e) => handleChange('college_name', e.target.value)}
          onBlur={() => handleBlur('college_name')}
          margin='dense'
          error={touched.college_name && !!errors.college_name}
          helperText={touched.college_name ? errors.college_name : ''}
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='Phone'
          value={editedTPO.phone_no}
          onChange={(e) => handleChange('phone_no', e.target.value)}
          onBlur={() => handleBlur('phone_no')}
          margin='dense'
          error={touched.phone_no && !!errors.phone_no}
          helperText={touched.phone_no ? errors.phone_no : ''}
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='Status'
          value={editedTPO.status}
          onChange={(e) => handleChange('status', e.target.value)}
          onBlur={() => handleBlur('status')}
          margin='dense'
          error={touched.status && !!errors.status}
          helperText={touched.status ? errors.status : ''}
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
      </DialogContent>
      <DialogActions>
        <ActionButton
          onClick={onClose}
          color='secondary'
          sx={{ borderRadius: '25px', marginBottom: '20px', marginRight: '9px' }}
        >
          Cancel
        </ActionButton>
        <ActionButton
          onClick={handleSave}
          variant='contained'
          color='primary'
          disabled={!isFormValid}
          sx={{ borderRadius: '25px', marginBottom: '20px', marginRight: '5px' }}
        >
          Save
        </ActionButton>
      </DialogActions>
    </Dialog>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchEditTpo: (data: any) => dispatch(Edit_Tpo.request(data)),
})

export default connect(null, mapDispatchToProps)(EditTPODialog)
