import React, { useState, useEffect, ChangeEvent } from 'react'
import { Box, Tab, Tabs } from '@mui/material'
import CollegeList from './Colleges'
import Colleges from './Colleges/Colleges'
import InstitutePage from './Institute/InstitutePage'
import TPOTable from './Tpo/TPOTable'
import ConsultancyTable from './Consultancies/ConsultancyTable'

const InstituteContent: React.FC = () => <Box p={2}>Institute Content</Box>
const TPOContent: React.FC = () => <Box p={2}>TPO Content</Box>
const ConsultanciesContent: React.FC = () => <Box p={2}>Consultancies Content</Box>

const Contacts: React.FC = () => {
  const storedTab = localStorage.getItem('activeTab')
  const initialTab = storedTab ? parseInt(storedTab) : 0

  const [activeTab, setActiveTab] = useState<number>(initialTab)

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
    localStorage.setItem('activeTab', newValue.toString())
  }

  const renderContent = () => {
    switch (activeTab) {
      case 0:
        return <TPOTable />
      case 1:
        return <InstitutePage />
      case 2:
        return <ConsultancyTable />
      case 3:
        return <CollegeList />
      default:
        return null
    }
  }

  return (
    <Box
      sx={{
        width: { xs: '90%', sm: '90%', lg: '93%' },
        margin: '25px auto 70px auto',
        backgroundColor: '#fff',
        padding: { xs: '1rem', sm: '1.5rem', lg: '25px' },
        paddingTop: { lg: '0.1rem' },
        borderRadius: { sm: '6px', lg: '4px' },
        boxShadow:
          '0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)',
        border: '1px solid #DDDDDD',
        opacity: 1,
      }}
    >
      <Box className='templates-page-tabs'>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label='Tabs for different tables'>
          <Tab label='TPO' />
          <Tab label='Institute' />
          <Tab label='Consultancies' />
          <Tab label='Colleges' />
        </Tabs>
      </Box>
      {renderContent()}
    </Box>
  )
}

export default Contacts
