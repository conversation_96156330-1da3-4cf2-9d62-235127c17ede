import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>T<PERSON>le, DialogContent, Dialog<PERSON>ctions, TextField, But<PERSON> } from '@mui/material'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { Edit_Tpo } from 'actions'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { toast } from 'react-toastify'

interface Consultancy {
  id: string | number
  college_name: string
  representative_type: string
  name: string
  email: string
  website: string
  phone_no: string
  last_touch: string | Date
  fresher_charges: string | number
  experience_charges: string | number
  status: string
}

interface EditConsultancyDialogProps {
  open: boolean
  onClose: () => void
  consultancy: Consultancy | null
  fetchEditTpo: (updatedTPO: any) => void
}

const EditConsultancyDialog: React.FC<EditConsultancyDialogProps> = ({
  open,
  onClose,
  consultancy,
  fetchEditTpo,
}) => {
  const [formData, setFormData] = useState<Consultancy | null>(consultancy)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    setFormData(consultancy)
  }, [consultancy])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!formData) return
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const handleSave = async () => {
    if (formData) {
      try {
        setLoading(true)
        setError(null)
        await fetchEditTpo(formData)
        onClose()
        toast.success('Consultancy Edited Successfully!')
      } catch (err) {
        setError('Failed to update consultancy. Please try again.')
      } finally {
        setLoading(false)
      }
    }
  }

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
      <DialogTitle
        sx={{ textAlign: 'center', backgroundColor: '#0d3c6e', color: 'white', mb: '15px' }}
      >
        Edit Consultancy
      </DialogTitle>
      <DialogContent>
        {error && <p style={{ color: 'red', textAlign: 'center' }}>{error}</p>}
        <TextField
          fullWidth
          label='Consultancy Name'
          name='college_name'
          value={formData?.college_name || ''}
          onChange={handleChange}
          margin='dense'
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='Representative Type'
          name='representative_type'
          value={formData?.representative_type || ''}
          onChange={handleChange}
          margin='dense'
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='Name'
          name='name'
          value={formData?.name || ''}
          onChange={handleChange}
          margin='dense'
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='Email'
          name='email'
          value={formData?.email || ''}
          onChange={handleChange}
          margin='dense'
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='Website'
          name='website'
          value={formData?.website || ''}
          onChange={handleChange}
          margin='dense'
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
        <TextField
          fullWidth
          label='Phone Number'
          name='phone_no'
          value={formData?.phone_no || ''}
          onChange={handleChange}
          margin='dense'
          InputProps={{
            sx: { borderRadius: '25px' },
          }}
        />
      </DialogContent>
      <DialogActions>
        <ActionButton
          onClick={onClose}
          disabled={loading}
          sx={{ borderRadius: '25px', marginBottom: '20px', marginRight: '9px' }}
        >
          Cancel
        </ActionButton>
        <ActionButton
          onClick={handleSave}
          variant='contained'
          color='primary'
          disabled={loading}
          sx={{ borderRadius: '25px', marginBottom: '20px', marginRight: '5px' }}
        >
          {loading ? 'Saving...' : 'Save'}
        </ActionButton>
      </DialogActions>
    </Dialog>
  )
}

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchEditTpo: (data: Consultancy) => dispatch(Edit_Tpo.request(data)),
})

export default connect(null, mapDispatchToProps)(EditConsultancyDialog)
