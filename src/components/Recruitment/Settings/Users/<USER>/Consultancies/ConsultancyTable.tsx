import React, { useEffect, useState } from 'react'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Box,
  IconButton,
  Pagination,
  TableSortLabel,
  Typography,
} from '@mui/material'
import { connect } from 'react-redux'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../../reducers'
import { RootState } from '../../../../../../configureStore'
import { DeleteTpo, fetchTpoDetails } from '../../../../../../actions'
import SearchBar from './SearchBar'
import FilterButton from './FilterButton'
import { Dispatch } from 'redux'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import AddConsultancy from './AddConsultancy'
import EditConsultancyDialog from './EditConsultancyDialoge'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { toast } from 'react-toastify'

function ConsultancyTable(props: any) {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [page, setPage] = useState<number>(1)
  const itemsPerPage: number = 6
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const handleOpenModal = () => setIsModalOpen(true)
  const [selectedConsultancy, setSelectedConsultancy] = useState<any>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedTPOId, setSelectedTPOId] = useState<number | null>(null)
  const { fetchTPOData, TPOOptions, deleteTpo, isConsultancyLoaded, isEditorLoaded } = props
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const handleEditClick = (consultancy: any) => {
    setSelectedConsultancy(consultancy)
    setEditDialogOpen(true)
  }

  const handleDeleteClick = (id: number) => {
    setSelectedTPOId(id)
    setDeleteDialogOpen(true)
  }
  useEffect(() => {
    fetchTPOData({ representative_type: 'Consultancy' })
  }, [isConsultancyLoaded, isEditorLoaded])

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleEmail = () => {
    toast.error('Email not found')
  }
  function handleAddConsultancy(newConsultancy: any): void {
    throw new Error('Function not implemented.')
  }

  const handleConfirm = () => {
    if (selectedTPOId !== null) {
      deleteTpo(selectedTPOId)
      setDeleteDialogOpen(false)
      setSelectedTPOId(null)
      toast.success('Consultancy Edited Successfully!')
    }
  }

  const handleSort = () => {
    setSortOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'))
  }

  const sortedData = [...(TPOOptions[0] || [])].sort((a, b) => {
    if (sortOrder === 'asc') {
      return a.name.localeCompare(b.name)
    } else {
      return b.name.localeCompare(a.name)
    }
  })

  const filteredData = sortedData.filter((row) =>
    row.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '10px',
        }}
      >
        <Box sx={{ maxWidth: '220px', flex: 1 }}>
          <SearchBar label='Search Name' value={searchTerm} onChange={setSearchTerm} />
        </Box>

        <Box sx={{ display: 'flex', gap: '10px' }}>
          <ActionButton
            variant='contained'
            sx={{
              backgroundColor: '#0d3c6e',
              color: 'white',
              borderRadius: '50px',
              padding: '8px 20px',
              fontFamily: 'Montserrat, sans-serif',
            }}
            onClick={handleOpenModal}
          >
            Add Consultancy
          </ActionButton>
          <ActionButton
            variant='contained'
            onClick={handleEmail}
            sx={{
              backgroundColor: '#0d3c6e',
              color: 'white',
              borderRadius: '50px',
              padding: '8px 20px',
              fontFamily: 'Montserrat, sans-serif',
            }}
          >
            Send Test Email
          </ActionButton>
          <ActionButton
            variant='contained'
            onClick={handleEmail}
            sx={{
              backgroundColor: '#0d3c6e',
              color: 'white',
              borderRadius: '50px',
              padding: '8px 20px',
              fontFamily: 'Montserrat, sans-serif',
            }}
          >
            Send Email
          </ActionButton>
        </Box>
      </Box>

      <FilterButton open={isFilterOpen} />

      <TableContainer component={Paper} sx={{ overflow: 'hidden', flex: 1 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#0d3c6e' }}>
              <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>
                <TableSortLabel
                  active={true}
                  direction={sortOrder}
                  onClick={handleSort}
                  sx={{
                    color: 'white',
                    '& .MuiTableSortLabel-icon': { color: 'white !important' },
                  }}
                ></TableSortLabel>
                Contact Person
              </TableCell>
              {[
                'Type',
                'Consultancy Name',
                'Email',
                'Website',
                'Phone Number',
                'Last Touch',
                'Fresher Charges',
                'Experience Charges',
                'Status',
                'Actions',
              ].map((heading) => (
                <TableCell
                  key={heading}
                  sx={{
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    fontFamily: 'Montserrat, sans-serif',
                  }}
                >
                  {heading}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredData.length > 0 ? (
              filteredData.slice((page - 1) * itemsPerPage, page * itemsPerPage).map((row) => (
                <TableRow key={row.id}>
                  <TableCell sx={{ textAlign: 'center' }}>{row.name}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.representative_type}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.college_name}</TableCell>

                  <TableCell sx={{ textAlign: 'center' }}>{row.email}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.website}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.phone_no}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.last_touch}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.fresher_charges}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.experience_charges}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>{row.status}</TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>
                    <IconButton onClick={() => handleEditClick(row)}>
                      <EditIcon color='primary' />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteClick(row.id)}>
                      <DeleteIcon color='error' />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={11} align='center'>
                  <Typography variant='h6' color='textSecondary'>
                    No data found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, pr: 2 }}>
        <Pagination
          count={Math.ceil(TPOOptions[0]?.length / itemsPerPage)}
          color='primary'
          page={page}
          onChange={handlePageChange}
        />
      </Box>

      <AddConsultancy
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAdd={function (newTPO: any): void {
          throw new Error('Function not implemented.')
        }}
      />
      <EditConsultancyDialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        consultancy={selectedConsultancy}
        // fetchEditTpo={fetchEditTpo}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={() => setDeleteDialogOpen(false)}
        handleConfirm={handleConfirm}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    TPOOptions: recruitmentEntity.getRecruitment(state).getTpoDetails,
    isConsultancyLoaded: recruitmentStateUI.getRecruitment(state).AddTpo,
    isEditorLoaded: recruitmentStateUI.getRecruitment(state).editTpo,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchTPOData: (data: {}) => dispatch(fetchTpoDetails.request({ data })),
    deleteTpo: (id: number) => dispatch(DeleteTpo.request({ id })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ConsultancyTable)
