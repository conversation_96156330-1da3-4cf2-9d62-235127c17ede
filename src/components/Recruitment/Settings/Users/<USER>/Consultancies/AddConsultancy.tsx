import React, { useEffect, useState } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  MenuItem,
  Box,
  IconButton,
  InputAdornment,
  Grid,
} from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import EventIcon from '@mui/icons-material/Event'
import dayjs, { Dayjs } from 'dayjs'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { AddTpo, viewOrganisationDetailsByType } from 'actions'
import { connect } from 'react-redux'
import { toast } from 'react-toastify'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

interface AddConsultancyProps {
  open: boolean
  onClose: () => void
  onAdd: (newAdd: any) => void
  addTpodetails: ({}) => {}
  OrganisationDetailsOptions: any
  fetchOrganisationDetails: ({}) => {}
}

const AddConsultancy: React.FC<AddConsultancyProps> = ({
  open,
  onClose,
  onAdd,
  OrganisationDetailsOptions,
  fetchOrganisationDetails,
  addTpodetails,
}) => {
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone_no: '',
    status: '',
    notes: '',
    website: '',
    experience_charges: '',
    fresher_charges: '',
    college_name: '',
    representative_type: 'Consultancy',
    last_touch: '',
  })

  const [errors, setErrors] = useState<any>({})
  const [touchedFields, setTouchedFields] = useState<{ [key: string]: boolean }>({})
  const [isFormValid, setIsFormValid] = useState(false)

  const inputStyles = {
    sx: {
      borderRadius: '50px',
      overflow: 'hidden',
    },
  }

  useEffect(() => {
    fetchOrganisationDetails({ representative_type: 'Consultancy' })
  }, [])

  useEffect(() => {
    validateForm()
  }, [form])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let { name, value } = e.target

    if (name === 'phone_no') {
      value = value.replace(/\D/g, '') // Only digits
    }

    setForm((prev) => ({ ...prev, [name]: value }))
    setErrors((prev: any) => ({ ...prev, [name]: '' }))
    setTouchedFields((prev) => ({ ...prev, [name]: true }))
  }

  const handleDateChange = (newValue: Dayjs | null) => {
    setForm((prev) => ({
      ...prev,
      last_touch: newValue ? newValue.format('YYYY-MM-DD') : '',
    }))
  }

  const validateForm = () => {
    const newErrors: any = {}

    if (!form.name.trim()) newErrors.name = 'Required'
    if (!form.email.trim()) {
      newErrors.email = 'Required'
    } else {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!pattern.test(form.email)) newErrors.email = 'Invalid email'
    }
    if (!form.phone_no.trim()) {
      newErrors.phone_no = 'Required'
    } else if (form.phone_no.length !== 10) {
      newErrors.phone_no = 'Must be 10 digits'
    }
    if (!form.status) newErrors.status = 'Required'
    if (!form.college_name) newErrors.college_name = 'Required'

    setErrors(newErrors)
    setIsFormValid(Object.keys(newErrors).length === 0)
  }

  const handleSubmit = () => {
    if (!isFormValid) return
    addTpodetails(form)
    onClose()
    toast.success('Consultancy Added Successfully!')
  }

  useEffect(() => {
    if (open) {
      setForm({
        name: '',
        email: '',
        phone_no: '',
        status: '',
        notes: '',
        website: '',
        experience_charges: '',
        fresher_charges: '',
        college_name: '',
        representative_type: 'Consultancy',
        last_touch: '',
      })
      setErrors({})
      setTouchedFields({})
      setIsFormValid(false)
    }
  }, [open])

  return (
    <Dialog open={open} onClose={onClose} maxWidth='md' fullWidth>
      <DialogTitle
        sx={{
          background: '#0d3c6e',
          color: 'white',
          textAlign: 'center',
          fontSize: 18,
          fontWeight: 'bold',
        }}
      >
        Add Consultancy
      </DialogTitle>
      <DialogContent sx={{ padding: 1, fontFamily: 'Montserrat' }}>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <TextField
              name='name'
              label='Name'
              value={form.name}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
              error={!!errors.name && touchedFields.name}
              helperText={touchedFields.name && errors.name}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              name='email'
              label='Email'
              value={form.email}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
              error={!!errors.email && touchedFields.email}
              helperText={touchedFields.email && errors.email}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              name='phone_no'
              label='Phone Number'
              value={form.phone_no}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
              error={!!errors.phone_no && touchedFields.phone_no}
              helperText={touchedFields.phone_no && errors.phone_no}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              name='website'
              label='Website'
              value={form.website}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
            />
          </Grid>
          <Grid item xs={6}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                label='Latest Touch'
                value={form.last_touch ? dayjs(form.last_touch) : null}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    InputProps: {
                      ...inputStyles,
                      endAdornment: (
                        <InputAdornment position='end'>
                          <IconButton>
                            <EventIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={6}>
            <TextField
              name='fresher_charges'
              label='Fresher Charges'
              value={form.fresher_charges}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              name='experience_charges'
              label='Experience Charges'
              value={form.experience_charges}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              select
              name='status'
              label='Status'
              value={form.status}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
              error={!!errors.status && touchedFields.status}
              helperText={touchedFields.status && errors.status}
            >
              <MenuItem value='Active'>Active</MenuItem>
              <MenuItem value='Inactive'>Inactive</MenuItem>
            </TextField>
          </Grid>

          <Grid item xs={6}>
            <TextField
              select
              name='college_name'
              label='Consultancy Name'
              value={form.college_name}
              onChange={handleChange}
              fullWidth
              InputProps={inputStyles}
              error={!!errors.college_name && touchedFields.college_name}
              helperText={touchedFields.college_name && errors.college_name}
            >
              {OrganisationDetailsOptions[0] &&
                OrganisationDetailsOptions[0].map((data: any) => (
                  <MenuItem key={data.organisation} value={data.organisation}>
                    {data.organisation}
                  </MenuItem>
                ))}
            </TextField>
          </Grid>
        </Grid>

        <Box mt={2}>
          <CKEditor
            editor={ClassicEditor}
            config={{}}
            data={form.notes}
            onChange={(event, editor) => {
              const data = editor.getData()
              setForm((prev) => ({ ...prev, notes: data }))
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, marginTop: 3 }}>
          <ActionButton
            onClick={onClose}
            sx={{
              borderRadius: '50px',
              padding: '9px 15x',
              backgroundColor: '#f5f5f5',
              marginRight: '20px',
              mb: '9px',
              color: '#0d3c6e',
              '&:hover': { backgroundColor: '#dcdcdc' },
            }}
          >
            Cancel
          </ActionButton>
          <ActionButton
            variant='contained'
            onClick={handleSubmit}
            disabled={!isFormValid}
            sx={{
              borderRadius: '50px',
              mr: '9px',
              mb: '9px',
              padding: '9px 15px',
              backgroundColor: isFormValid ? '#0d3c6e' : '#aaa',
              color: 'white',
              '&:hover': {
                backgroundColor: isFormValid ? '#092a4d' : '#aaa',
              },
            }}
          >
            Submit
          </ActionButton>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

const mapStateToProps = (state: RootState) => ({
  OrganisationDetailsOptions: recruitmentEntity.getRecruitment(state).viewOrganisationDetailsByType,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchOrganisationDetails: (data: {}) => dispatch(viewOrganisationDetailsByType.request({ data })),
  addTpodetails: (data: {}) => dispatch(AddTpo.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(AddConsultancy)
