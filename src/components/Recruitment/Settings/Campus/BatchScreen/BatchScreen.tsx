import React, { useEffect, useState } from 'react'
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Card,
  InputAdornment,
  TextField,
  TableSortLabel,
} from '@mui/material'
import BatchModal from './BatchModal'
import ActionCell from '../../ActionCell'
import CustomPagination from '../../CustomPagination'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import { RootState } from '../../../../../configureStore'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { addManageBatches, deleteBatch, editBatch, fetchBatches } from '../../../../../actions'
import { toast } from 'react-toastify'
import SearchIcon from '@mui/icons-material/Search';
import Loader from 'components/Common/Loader'

function BatchScreen(props: any) {
  const [openModal, setOpenModal] = useState(false)
  const [batches, setBatches] = useState<{ id: number; batch: string }[]>([])
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentBatch, setCurrentBatch] = useState<string>('')
  const [page, setPage] = useState(1)
  const itemsPerPage = 10
  const totalPages = Math.ceil(
    batches.filter((batch) => batch.batch.toLowerCase().includes(searchTerm.toLowerCase())).length /
    itemsPerPage
  )
  const [isClicked, setIsClicked] = useState(false)
  const { fetchBatchesData, batchesOptions, addManageBatches, deleteBatch, isgetBatchesData } =
    props
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')

  const filteredBatches = batches.filter((batch) =>
    batch.batch.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const paginatedBatches = filteredBatches.slice((page - 1) * itemsPerPage, page * itemsPerPage)

  const sortedBatches = [...paginatedBatches].sort((a, b) => {
    return order === 'asc' ? a.batch.localeCompare(b.batch) : b.batch.localeCompare(a.batch)
  })

  const handleClick = () => {
    setIsClicked(true)
    setTimeout(() => setIsClicked(false), 200)
  }

  const handleOpenModal = (batch?: string, id?: number) => {
    if (batch !== undefined && id !== undefined) {
      setCurrentBatch(batch)
      setEditIndex(id)
    } else {
      setCurrentBatch('')
      setEditIndex(null)
    }
    setOpenModal(true)
  }

  const handleCloseModal = () => {
    setOpenModal(false)
    setEditIndex(null)
    setCurrentBatch('')
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    if (/^[\d-]*$/.test(value)) {
      setSearchTerm(value)
      setPage(1)
    }
  }

  const handleSaveBatch = (newBatch: string) => {
    if (editIndex !== null) {
      const updatedBatch = { id: editIndex, batch: newBatch }
      setBatches((prev) => prev.map((b) => (b.id === updatedBatch.id ? updatedBatch : b)))
      props.editBatch(updatedBatch)
      toast.success('Batch Edited Successfully')
      setTimeout(() => {
        props.fetchBatchesData()
      }, 500)
    } else {
      const newEntry = { batch: newBatch }
      toast.success('Batch Added Successfully')
      setTimeout(() => {
        props.fetchBatchesData()
      }, 500)
      props.addManageBatches(newEntry)
    }
    handleCloseModal()
  }

  const handleDeleteBatch = (id: number) => {
    setBatches((prev) => prev.filter((b) => b.id !== id))
    props.deleteBatch(id)
    toast.success('Batch Deleted Successfully')
    setTimeout(() => {
      props.fetchBatchesData()
    }, 500)
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  useEffect(() => {
    fetchBatchesData()
  }, [])

  useEffect(() => {
    if (Array.isArray(batchesOptions)) {
      const formattedData = batchesOptions.flat().map((item: any) => ({
        id: item.id,
        batch: item.batch,
      }))
      setBatches(formattedData)
    }
  }, [batchesOptions])

  return (
    <Box sx={{ p: 3, fontFamily: 'Montserrat-Medium', maxWidth: '100%' }}>
      <Card
        sx={{
          padding: '20px',
          width: '100%',
          justifyContent: 'center',
          margin: 'auto',
          maxWidth: '98%',
        }}
      >
        <Typography
          variant='h6'
          sx={{
            fontFamily: 'Montserrat-Semibold',
            fontWeight: 600,
            position: 'relative',
            display: 'inline-block',
            padding: '8px 16px',
            color: 'primary.main',
            borderRadius: '4px',
            cursor: 'pointer',
            transition: 'background-color 0.3s ease-in-out, color 0.3s ease-in-out',
            backgroundColor: isClicked ? 'rgba(25, 60, 109, 0.3)' : 'transparent',
            '&::after': {
              content: '""',
              display: 'block',
              width: '100%',
              height: '2px',
              backgroundColor: '#193C6D',
              position: 'absolute',
              bottom: '0px',
              left: 0,
            },
          }}
          onClick={handleClick}
        >
          Batches
        </Typography>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 0,
            gap: 2,
          }}
        >
          <Box sx={{ flex: 1 }}>
            <TextField
              variant='outlined'
              placeholder='Search Batch'
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>
                    <SearchIcon color='action' />
                  </InputAdornment>
                ),
                sx: {
                  borderRadius: '50px',
                  height: '40px',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '50px',
                  },
                },
              }}
              sx={{
                flex: 1,
                minWidth: '180px',
                maxWidth: '14%',
                '& .MuiInputBase-input': {
                  fontFamily: 'Montserrat-Medium',
                },
              }}
            />
          </Box>
          <Box>
            <Button
              variant='contained'
              color='secondary'
              onClick={() => handleOpenModal()}
              sx={{
                fontFamily: 'Montserrat-Semibold',
                borderRadius: '24px',
                fontSize: '16px',
                paddingLeft: '28px',
                paddingRight: '22px',
                fontWeight: '700',
              }}
            >
              Add Batch
            </Button>
          </Box>
        </Box>
        <Loader state={!isgetBatchesData} />
        <Card sx={{ borderRadius: '4px' }}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead sx={{ bgcolor: '#193C6D' }}>
                <TableRow>
                  <TableCell
                    sx={{
                      fontFamily: 'Montserrat-Medium',
                      fontWeight: 600,
                      color: 'white',
                      textAlign: 'center',
                      fontSize: '15px',
                      width: '50%',
                    }}
                  >
                    <TableSortLabel
                      active={true}
                      direction={order}
                      onClick={() => setOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'))}
                      sx={{
                        color: 'white',
                        '& .MuiTableSortLabel-icon': {
                          color: 'white !important',
                        },
                      }}
                    >
                      <span style={{ color: 'white', fontWeight: 600 }}>Batches</span>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell
                    sx={{
                      fontFamily: 'Montserrat-Medium',
                      fontWeight: 600,
                      color: 'white',
                      textAlign: 'center',
                      fontSize: '15px',
                      width: '50%',
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sortedBatches.length > 0 ? (
                  sortedBatches.map((batch) => (
                    <TableRow key={batch.id} sx={{ borderBottom: '1px solid #ddd' }}>
                      <TableCell
                        sx={{
                          fontFamily: 'Montserrat-Medium',
                          fontWeight: 600,
                          textAlign: 'center',
                        }}
                      >
                        {batch.batch}
                      </TableCell>
                      <TableCell
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          textAlign: 'center',
                        }}
                      >
                        <ActionCell
                          onEdit={() => handleOpenModal(batch.batch, batch.id)}
                          onDelete={() => handleDeleteBatch(batch.id)}
                        />
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={2}
                      align='center'
                      sx={{
                        fontFamily: 'Montserrat-Medium',
                        fontWeight: 600,
                        py: 3,
                        color: 'black',
                      }}
                    >
                      No records found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, pr: 2 }}>
          <CustomPagination count={totalPages} page={page} onChange={handlePageChange} />
        </Box>
      </Card>
      <BatchModal
        open={openModal}
        onClose={handleCloseModal}
        onAdd={handleSaveBatch}
        initialData={
          editIndex !== null ? batches.find((b) => b.id === editIndex)?.batch : undefined
        }
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    batchesOptions: recruitmentEntity.getRecruitment(state).getBatchesData,
    isgetBatchesData: recruitmentStateUI.getRecruitment(state).isgetBatchesData,
    deleteBatch: recruitmentEntity.getRecruitment(state).deleteBatch,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchBatchesData: () => dispatch(fetchBatches.request()),
    addManageBatches: (data: any) => dispatch(addManageBatches.request({ data })),
    deleteBatch: (id: number) => dispatch(deleteBatch.request({ id })),
    editBatch: (data: { id: number; batch: string }) => dispatch(editBatch.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(BatchScreen)