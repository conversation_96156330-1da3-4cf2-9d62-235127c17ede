import React, { useEffect, useState } from 'react'
import {
  Box,
  Pagination,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import {
  AddRoundDialog,
  RoundsHeader,
  EditRoundDialog,
  RoundsTableHeader,
  RoundsTableRow,
  handleEditClick,
} from './RoundsTableImports'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { fetchRounds, fetchrRoundType } from '../../../../actions'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { Round, Rounds } from './RoundsTypes'
import { recruitmentState } from 'reducers/entities'
import Loader from 'components/Common/Loader'

interface Props {
  roundOptions: Rounds
  fetchRoundsData: () => void
  roundTypeOptions: any
  fetchRoundsTypeData: () => void
  isRoundDeleted: boolean
  getAddRound: any
  isRoundDEdited: boolean
  getRoundLoaderData: any
}

function RoundsTable({
  roundOptions,
  fetchRoundsData,
  roundTypeOptions,
  fetchRoundsTypeData,
  isRoundDeleted,
  getAddRound,
  isRoundDEdited,
  getRoundLoaderData,
}: Props) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [selectedRound, setSelectedRound] = useState<Round | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')
  const itemsPerPage = 10

  useEffect(() => {
    fetchRoundsData()
    fetchRoundsTypeData()
  }, [isRoundDeleted, , getAddRound, isRoundDEdited])

  const Data = roundTypeOptions[0] ? roundTypeOptions[0] : []

  useEffect(() => {
    setPage(1)
  }, [roundOptions])

  const sortedRounds = Array.isArray(roundOptions[0])
    ? [...roundOptions[0]]
        .filter((item) => item?.round_name)
        .sort((a, b) => {
          const nameA = a?.round_name?.toLowerCase() || ''
          const nameB = b?.round_name?.toLowerCase() || ''
          return order === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA)
        })
    : []

  const handleSort = (newOrder: 'asc' | 'desc') => {
    setOrder(newOrder)
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const filteredData = sortedRounds.filter((row) =>
    row.round_name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <>
      <Loader> state={!getRoundLoaderData} </Loader>
      <Box sx={{ width: '98%', padding: '20px' }}>
        <Paper sx={{ px: '2rem' }}>
          <RoundsHeader
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            onAddClick={() => setOpen(true)}
          />

          <Paper
            sx={{
              width: '100%',
              height: 'calc(100% - 100px)',
              overflow: 'hidden',
              borderRadius: '0px',
              boxShadow: 3,
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <TableContainer sx={{ flexGrow: 1, overflow: 'auto' }}>
              <Table stickyHeader>
                <RoundsTableHeader order={order} onSort={handleSort} />
                <TableBody>
                  {filteredData.length > 0 ? (
                    filteredData
                      .slice((page - 1) * itemsPerPage, page * itemsPerPage)
                      .map((row) => (
                        <RoundsTableRow
                          key={row.id}
                          row={row}
                          onEdit={() => handleEditClick(row, setSelectedRound, setEditDialogOpen)}
                          roundTypeOptions={roundTypeOptions}
                        />
                      ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align='center'>
                        <Typography variant='h6' color='textSecondary'>
                          No data found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          <AddRoundDialog open={open} onClose={() => setOpen(false)} roundTypeOptions={Data} />

         

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'flex-end',
              mt: 2,
              pr: 2,
              marginBottom: '4px',
              padding: '3px',
            }}
          >
            <Pagination
              count={Math.ceil(roundOptions[0]?.length / itemsPerPage)}
              color='primary'
              page={page}
              onChange={handlePageChange}
            />
          </Box>
        </Paper>
      </Box>
    </>
  )
}

const mapStateToProps = (state: RootState) => ({
  roundOptions: recruitmentEntity.getRecruitment(state).getRoundData,
  roundTypeOptions: recruitmentEntity.getRecruitment(state).getRoundType,
  isRoundDeleted: recruitmentStateUI.getRecruitment(state).isRoundDeleted,
  isRoundDEdited: recruitmentStateUI.getRecruitment(state).editRounds,
  getAddRound: recruitmentStateUI.getRecruitment(state).getAddRound,
  getRoundLoaderData: recruitmentStateUI.getRecruitment(state).getRoundLoadData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchRoundsData: () => dispatch(fetchRounds.request()),
  fetchRoundsTypeData: () => dispatch(fetchrRoundType.request()),
})

export default connect(mapStateToProps, mapDispatchToProps)(RoundsTable)
