import React from 'react'
import { TableHead, TableRow, TableCell, TableSortLabel } from '@mui/material'

interface Props {
  order: 'asc' | 'desc'
  onSort: (newOrder: 'asc' | 'desc') => void
}

const RoundsTableHeader: React.FC<Props> = ({ order, onSort }) => {
  const handleSort = () => {
    onSort(order === 'asc' ? 'desc' : 'asc')
  }

  return (
    <TableHead>
      <TableRow sx={{ backgroundColor: '#1f2d3d' }}>
        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
          }}
        >
          <TableSortLabel
            active
            direction={order}
            onClick={handleSort}
            sx={{
              color: 'white',

              '& .MuiTableSortLabel-icon': { color: 'white !important' },
            }}
          ></TableSortLabel>
          Name
        </TableCell>

        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
          }}
        >
          Round Type
        </TableCell>

        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
          }}
        >
          Description
        </TableCell>

        <TableCell
          sx={{
            textAlign: 'center',
            fontWeight: 'bold',
            color: '#fff',
            backgroundColor: 'rgb(25,60,109)',
          }}
        >
          Actions
        </TableCell>
      </TableRow>
    </TableHead>
  )
}

export default RoundsTableHeader
