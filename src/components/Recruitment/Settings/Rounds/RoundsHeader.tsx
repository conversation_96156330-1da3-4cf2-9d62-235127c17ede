import React from 'react'
import { <PERSON>, <PERSON>po<PERSON>, Button, Tabs, Tab } from '@mui/material'

import SearchInput from './SearchInput'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

interface RoundsHeaderProps {
  searchTerm: string
  setSearchTerm: (value: string) => void
  onAddClick: () => void
}

const RoundsHeader: React.FC<RoundsHeaderProps> = ({ searchTerm, setSearchTerm, onAddClick }) => {
  return (
    <>
      <Box className='templates-page-tabs'>
        <Tabs value={0} aria-label='Tabs for different tables' sx={{ marginLeft: '2px' }}>
          <Tab label='Rounds' sx={{ fontSize: '19px', fontWeight: 'bold' }} />
        </Tabs>
      </Box>

      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '8px 16px',
          marginBottom: 1,
          marginLeft: 0,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            width: '270px',
            fontFamily: 'Montserrat, sans-serif',
          }}
        >
          <SearchInput label='Search by Name' value={searchTerm} onChange={setSearchTerm} />
        </Box>

        <ActionButton onClick={onAddClick}>Add Round</ActionButton>
      </Box>
    </>
  )
}

export default RoundsHeader
