import React, { useState } from 'react'
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Box,
  Button,
  TextField,
} from '@mui/material'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { addStaticData } from '../../../../../../src/actions'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import { toast } from 'react-toastify'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity } from '../../../../../reducers'
interface AddStaticDataProps {
  open: boolean
  onClose: () => void
  onSave: (newRound: { key: string; value: string }) => void
}


const AddStaticData: React.FC<AddStaticDataProps> = ({ open, onClose, onSave }) => {
  const [key, setKey] = useState('')
  const [value, setValue] = useState('')
  const handleSave = async () => {
    if (key.trim() && value.trim()) {
      const newStaticData = { key, value };
  
      try {
        const response = await onSave(newStaticData); 
  
          toast.success('Data added successfully!');
        } 
        
       catch (error) {
        toast.error( 'Something went wrong');
      }
  
      setKey('');
      setValue('');
      onClose();
    }
  };
  
  const isFormValid = ()=>{
    if(key.trim()!='' && value.trim() != '')
      return false;
    else return true;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      disableEnforceFocus
      sx={{ '& .MuiPaper-root': { borderRadius: '16px' } }}
    >
      <DialogTitle margin='auto'>Add Static Data</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label='Key'
          value={key}
          onChange={(e) => setKey(e.target.value)}
          margin='dense'
          sx={{
            borderRadius: '16px',
            '& .MuiOutlinedInput-root': { borderRadius: '16px' },
          }}
        />
        <Box
           sx={{
            width: '500px',
            maxHeight: '250px',
            overflowY: 'auto', 
            padding: '2px',
            border: '1px solid #ccc',
            borderRadius: '8px', 
          }}
        >
          <CKEditor
            editor={ClassicEditor}
            data={value}
            onReady={() => {}}
            onChange={(event, editor) => {
              const data = editor.getData()
              setValue(data)
            }}
            
          />
        </Box>
        
      </DialogContent>

      <DialogActions>
        <Box display='flex' justifyContent='center' gap={1} mt={0}>
          <Button
            variant='contained'
            sx={{
              backgroundColor: 'rgb(226, 226, 226)',
              color: 'black',
              padding: '8px 16px',
              borderRadius: '25px',
              fontFamily: 'Montserrat, sans-serif',
              fontSize: '14px',
              minWidth: '100px',
              '&:hover': {
                backgroundColor: 'rgb(226, 226, 226)',
                color: 'black',
              },
            }}
            onClick={onClose}
          >
            CANCEL
          </Button>

          <Button
            type='submit'
            variant='contained'
            sx={{
              color: 'white',
              padding: '8px 16px',
              borderRadius: '25px',
              fontFamily: 'Montserrat, sans-serif',
              fontSize: '14px',
              minWidth: '100px',
            }}
            onClick={handleSave}
            disabled={isFormValid()}
          >
            CREATE
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    response: recruitmentEntity.getRecruitment(state).postStaticData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    addStaticData: (data: any) => dispatch(addStaticData.request(data)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(AddStaticData)
