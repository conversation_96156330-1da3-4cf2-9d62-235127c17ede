import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Tooltip,
  Card,
  TableSortLabel,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { BlockedSubjectListProps } from './BlockedSubjectType'
import Loader from 'components/Common/Loader'
import { StyledTableCell } from 'components/Common/CommonStyles'
import { useState, useMemo } from 'react'

const BlockedSubjectList: React.FC<BlockedSubjectListProps> = ({
  paginatedBlockedSubjects,
  handleEdit,
  handleDelete,
  isBlockedSubjectData,
}) => {
  const [expandedSubject, setExpandedSubject] = useState<number | null>(null)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const handleSort = () => {
    setSortOrder((prev) => (prev === 'asc' ? 'desc' : 'asc'))
  }

  const sortedBlockedSubjects = useMemo(() => {
    return [...paginatedBlockedSubjects].sort((a, b) => {
      if (sortOrder === 'asc') {
        return a.subject.localeCompare(b.subject)
      } else {
        return b.subject.localeCompare(a.subject)
      }
    })
  }, [paginatedBlockedSubjects, sortOrder])

  return (
    <>
      <Loader state={!isBlockedSubjectData} />
      <Card sx={{ borderRadius: '4px' }}>
        <Table>
          <TableHead sx={{ bgcolor: '#193C6D' }}>
            <TableRow sx={{ height: '50px !important' }}>
              <StyledTableCell
                sx={{
                  fontFamily: 'Montserrat-Medium',
                  color: 'white',
                  fontSize: '13px !important',
                  width: '50% !important',
                  cursor: 'pointer',
                }}
              >
                Subject Name
                <TableSortLabel
                  active
                  direction={sortOrder}
                  onClick={handleSort}
                  sx={{
                    color: 'white',
                    '& .MuiTableSortLabel-icon': { color: 'white !important' },
                  }}
                ></TableSortLabel>
              </StyledTableCell>
              <StyledTableCell
                sx={{
                  fontFamily: 'Montserrat-Medium',
                  color: 'white',
                  textAlign: 'right',
                  fontSize: '16px',
                  width: '50% !important',
                }}
              >
                Actions
              </StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedBlockedSubjects.length > 0 ? (
              sortedBlockedSubjects.map((blockSub, index) => (
                <TableRow
                  key={blockSub.id}
                  sx={{ borderBottom: '1px solid #ddd', height: '50px !important' }}
                >
                  <StyledTableCell
                    sx={{
                      width: '50% !important',
                      fontFamily: 'Montserrat-Medium',
                      fontSize: '12px !important',
                      textAlign: 'left',
                      maxWidth: '300px',
                      whiteSpace: expandedSubject === blockSub.id ? 'normal' : 'nowrap',
                      overflow: expandedSubject === blockSub.id ? 'visible' : 'hidden',
                      textOverflow: expandedSubject === blockSub.id ? 'clip' : 'ellipsis',
                      wordBreak: 'break-word',
                      cursor: 'pointer',
                    }}
                    onClick={() =>
                      setExpandedSubject(expandedSubject === blockSub.id ? null : blockSub.id)
                    }
                  >
                    {expandedSubject === blockSub.id
                      ? blockSub.subject
                      : blockSub.subject.length > 40
                      ? `${blockSub.subject.substring(0, 40)}...`
                      : blockSub.subject}
                  </StyledTableCell>

                  <StyledTableCell
                    sx={{
                      textAlign: 'right',
                      width: '50% !important',
                      fontFamily: 'Montserrat-Medium',
                    }}
                  >
                    <Tooltip title='Edit'>
                      <IconButton sx={{ color: '#193C6D' }} onClick={() => handleEdit(index)}>
                        <EditIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title='Delete'>
                      <IconButton sx={{ color: '#db3700' }} onClick={() => handleDelete(index)}>
                        <DeleteIcon sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                  </StyledTableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={2}
                  align='center'
                  sx={{ fontFamily: 'Montserrat-Medium', color: '#888' }}
                >
                  No data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>
    </>
  )
}

export default BlockedSubjectList
