import { useEffect, useState } from 'react'
import { Box, Card, TextField, Button, useMediaQuery } from '@mui/material'
import BlockedSubjectHeader from './BlockedSubjectHeader'
import BlockedSubjectList from './BlockedSubjectList'
import BlockedSubjectDialog from './BlockedSubjectDialog'
import HandlePagination from '../../HandlePagination'
import BlockedSubjectTitle from './BlockedSubjectTitle'
import {
  addRejectedSubjects,
  deleteRejectedSubjects,
  editRejectedSubjects,
  fetchBlockedSubjects,
} from '../../../../../actions'
import { RootState } from '../../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../../reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import DeleteConfirmation from '../../DeleteConfirmation'
import { BlockSubjects } from './BlockedSubjectType'
import { BlockedSubjectProps } from './BlockedSubjectType'

const BlockedSubject: React.FC<BlockedSubjectProps> = ({
  blockedSubjectOptions,
  fetchblockedSubjectsData,
  addrejectedSubjectsData,
  editrejectedSubjectsData,
  deleteblockedSubjectsData,
  isBlockedSubjectData,
}) => {
  const [blockedSubjects, setBlockedSubjects] = useState<BlockSubjects[]>([])
  const [open, setOpen] = useState<boolean>(false)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null)
  const [newBlockedSubject, setNewBlockedSubject] = useState<string>('')
  const [page, setPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [subjectExists, setSubjectExists] = useState<boolean>(false)

  const isMobile = useMediaQuery('(max-width:600px)')
  const isTablet = useMediaQuery('(max-width:900px)')

  const itemsPerPage = 10
  const filteredBlockedSubjects = blockedSubjects.filter((subject) =>
    subject.subject.toLowerCase().includes(searchTerm.toLowerCase()),
  )
  const totalPages = Math.ceil(filteredBlockedSubjects.length / itemsPerPage)
  const paginatedBlockedSubjects = filteredBlockedSubjects.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage,
  )

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const handleOpen = () => {
    setEditIndex(null)
    setNewBlockedSubject('')
    setOpen(true)
  }

  const handleClose = () => setOpen(false)

  const handleEdit = (index: number) => {
    const actualIndex = (page - 1) * itemsPerPage + index
    setEditIndex(filteredBlockedSubjects[actualIndex].id)
    setNewBlockedSubject(filteredBlockedSubjects[actualIndex].subject)
    setOpen(true)
  }

  const handleDelete = (index: number) => {
    const actualIndex = (page - 1) * itemsPerPage + index
    setDeleteIndex(filteredBlockedSubjects[actualIndex].id)
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
    setPage(1)
  }
  const checkSubjectExists = (subject: string) => {
    const exists = blockedSubjects.some(
      (item) => item.subject.toLowerCase() === subject.toLowerCase(),
    )
    setSubjectExists(exists)
    return exists
  }
  const handleSaveBlockedSubject = (newBlockedSubject: string) => {
    if (checkSubjectExists(newBlockedSubject)) {
      return
    }

    if (editIndex !== null) {
      editrejectedSubjectsData({ id: editIndex, subject: newBlockedSubject })
      setBlockedSubjects((prev) =>
        [
          ...prev.map((sub) =>
            sub.id === editIndex ? { ...sub, subject: newBlockedSubject } : sub,
          ),
        ].sort((a, b) => a.subject.localeCompare(b.subject)),
      )
    } else {
      addrejectedSubjectsData({ subject: newBlockedSubject })
      setBlockedSubjects((prev) =>
        [...prev, { id: Date.now(), subject: newBlockedSubject }].sort((a, b) =>
          a.subject.localeCompare(b.subject),
        ),
      )
    }
    setTimeout(() => {
      fetchblockedSubjectsData()
    }, 500)
    handleClose()
  }

  const handleConfirmDelete = () => {
    if (deleteIndex !== null) {
      deleteblockedSubjectsData(deleteIndex)
      setTimeout(() => {
        fetchblockedSubjectsData()
      }, 500)
      setDeleteIndex(null)
    }
  }
  const handleSubjectChange = (value: string) => {
    setNewBlockedSubject(value)
    if (subjectExists) {
      setSubjectExists(false)
    }
  }
  useEffect(() => {
    fetchblockedSubjectsData()
  }, [])

  useEffect(() => {
    if (blockedSubjectOptions) {
      setBlockedSubjects(
        [...blockedSubjectOptions].sort((a, b) => a.subject.localeCompare(b.subject)),
      )
    }
  }, [blockedSubjectOptions])

  return (
    <Box
      sx={{
        fontFamily: 'Montserrat-Medium',
        maxWidth: '100%',
        margin: isMobile ? '5px' : '10px',
      }}
    >
      <Card
        sx={{
          padding: isMobile ? '10px' : '20px',
          width: isMobile ? '100%' : isTablet ? '90%' : '95%',
          margin: isMobile ? '10px auto' : '20px auto',
        }}
      >
        <BlockedSubjectTitle />
        <BlockedSubjectHeader
          handleOpen={handleOpen}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
        />
        <BlockedSubjectList
          paginatedBlockedSubjects={paginatedBlockedSubjects}
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          isBlockedSubjectData={isBlockedSubjectData}
        />
        <Box
          sx={{
            display: 'flex',
            justifyContent: isMobile ? 'center' : 'flex-end',
            mt: 2,
            pr: isMobile ? 0 : 2,
          }}
        >
          <HandlePagination count={totalPages} page={page} onChange={handlePageChange} />
        </Box>
      </Card>
      <BlockedSubjectDialog
        open={open}
        handleClose={handleClose}
        newBlockedSubject={newBlockedSubject}
        setNewBlockedSubject={handleSubjectChange}
        handleSaveBlockedSubject={handleSaveBlockedSubject}
        editIndex={editIndex}
        subjectExists={subjectExists}
      />
      <DeleteConfirmation
        open={deleteIndex !== null}
        handleClose={() => setDeleteIndex(null)}
        handleConfirm={handleConfirmDelete}
      />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    blockedSubjectOptions: recruitmentEntity.getRecruitment(state).getBlockedSubjectData,
    isBlockedSubjectData: recruitmentStateUI.getRecruitment(state).isBlockedSubjectData,
  }
}
const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchblockedSubjectsData: () => dispatch(fetchBlockedSubjects.request()),
    addrejectedSubjectsData: (data: { subject: string }) =>
      dispatch(addRejectedSubjects.request({ data })),
    editrejectedSubjectsData: (payload: { id: number; subject: string }) =>
      dispatch(editRejectedSubjects.request(payload)),
    deleteblockedSubjectsData: (id: number) => dispatch(deleteRejectedSubjects.request({ id })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(BlockedSubject)
