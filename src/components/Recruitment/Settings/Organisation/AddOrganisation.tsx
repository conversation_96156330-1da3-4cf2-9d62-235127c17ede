import React, { useEffect, useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Button,
  Box,
  MenuItem,
} from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity } from '../../../../reducers'
import { Dispatch } from 'redux'
import { fetchAddOrganisationDetails, fetchEditOrgDetails } from '../../../../actions'
import { connect } from 'react-redux'
import { toast } from 'react-toastify'

interface Organization {
  id?: number
  organisation: string
  city: string
  representative_type: string
  last_invited: string
}

interface AddOrganizationDialogProps {
  open: boolean
  onClose: () => void
  fetchAddOrganisationDetails: (data: Organization) => void
  fetchEditOrgDetails: (data: Organization) => void
  initialData?: Organization | null
  representativeOptions: any[]
  onSuccess?: () => void
}

const AddOrganizationDialog: React.FC<AddOrganizationDialogProps> = ({
  open,
  onClose,
  fetchAddOrganisationDetails,
  fetchEditOrgDetails,
  initialData,
  representativeOptions,
  onSuccess,
}) => {
  const [representative_type, setRepresentative] = useState('')
  const [organisation, setOrganisation] = useState('')
  const [city, setCity] = useState('')
  const [last_invited, setDate] = useState<Dayjs | null>(null)
  const [errors, setErrors] = useState({
    organisation: '',
    city: '',
    representative_type: '',
    last_invited: '',
  })

  useEffect(() => {
    if (initialData) {
      setOrganisation(initialData.organisation)
      setCity(initialData.city)
      setRepresentative(initialData.representative_type || '')
      setDate(initialData.last_invited ? dayjs(initialData.last_invited) : null)
    } else {
      setOrganisation('')
      setCity('')
      setRepresentative('')
      setDate(null)
    }
  }, [initialData, open])

  const validateField = (name: string, value: any) => {
    let error = ''

    if (name === 'organisation') {
      if (!value.trim()) error = 'Organization name is required'
    }

    if (name === 'city') {
      if (!value.trim()) error = 'City is required'
    }

    if (name === 'representative_type') {
      if (!value) error = 'Please select a representative type'
    }

    if (name === 'last_invited') {
      if (!value) error = 'Please select a date'
    }

    setErrors((prev) => ({ ...prev, [name]: error }))
  }

  const isFormValid =
    organisation.trim() &&
    city.trim() &&
    representative_type &&
    last_invited &&
    !Object.values(errors).some((error) => error !== '')

  const handleSubmit = () => {
    validateField('organisation', organisation)
    validateField('city', city)
    validateField('representative_type', representative_type)
    validateField('last_invited', last_invited)

    if (isFormValid) {
      const payload = {
        id: initialData?.id,
        organisation,
        city,
        representative_type,
        last_invited: last_invited?.format('MM-DD-YYYY'),
      }

      if (initialData && initialData.id) {
        fetchEditOrgDetails(payload)
        toast.success('Organization Updated successfully!')
      } else {
        fetchAddOrganisationDetails(payload)
        toast.success('Organization added successfully!')
      }

      setTimeout(() => {
        if (onSuccess) {
          onSuccess()
        }
        onClose()
      }, 500)
    }
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
        <DialogTitle sx={{ backgroundColor: '#1a2a50', color: 'white', textAlign: 'center', fontFamily: 'Montserrat-Medium' }}>
          {initialData ? 'Edit Organization' : 'Add Organization'}
        </DialogTitle>
        <DialogContent sx={{ padding: '10px', margin: '10px', width: '92%' }}>
          <Box>
            <TextField
              label='Organization Name'
              fullWidth
              margin='dense'
              value={organisation}
              onChange={(e) => {
                setOrganisation(e.target.value)
                validateField('organisation', e.target.value)
              }}
              error={!!errors.organisation}
              helperText={errors.organisation}
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
            />
          </Box>
          <Box>
            <TextField
              label='City'
              fullWidth
              margin='dense'
              value={city}
              onChange={(e) => {
                setCity(e.target.value)
                validateField('city', e.target.value)
              }}
              error={!!errors.city}
              helperText={errors.city}
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
            />
          </Box>
          <Box>
            <TextField
              select
              fullWidth
              label='Representative'
              margin='dense'
              variant='outlined'
              value={representative_type}
              onChange={(e) => {
                setRepresentative(e.target.value)
                validateField('representative_type', e.target.value)
              }}
              error={!!errors.representative_type}
              helperText={errors.representative_type}
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
            >
              {representativeOptions.map((rep: any) => (
                <MenuItem key={rep.id} value={rep.representative_type}>
                  {rep.representative_type}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          <Box>
            <DatePicker
              label='Date'
              value={last_invited}
              onChange={(newValue) => {
                setDate(newValue)
                validateField('last_invited', newValue)
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                  margin: 'dense',
                  variant: 'outlined',
                  error: !!errors.last_invited,
                  helperText: errors.last_invited,
                },
              }}
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
            />
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            paddingTop: '0px',
            paddingBottom: '30px',
            justifyContent: 'flex-end',
            paddingRight: '30px',
          }}
        >
          <Button
            onClick={onClose}
            variant='contained'
            sx={{
              backgroundColor: '#e2e2e2',
              color: '#000',
              borderRadius: '40px',
              '&:hover': { backgroundColor: 'rgb(226, 226, 226)', color: 'black' },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            color='primary'
            variant='contained'
            sx={{ borderRadius: '40px', padding: '7px 16px', fontWeight: 'bold' }}
            disabled={!isFormValid}
          >
            {initialData ? 'Update' : 'Submit'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  )
}

const mapStateToProps = (state: RootState) => ({
  AddOrganisationOptions: recruitmentEntity.getRecruitment(state).getAddOrganisationDetails,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchAddOrganisationDetails: (data: Organization) =>
    dispatch(fetchAddOrganisationDetails.request({ data })),
  fetchEditOrgDetails: (data: Organization) => dispatch(fetchEditOrgDetails.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(AddOrganizationDialog)
