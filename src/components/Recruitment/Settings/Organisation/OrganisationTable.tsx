import React, { useEffect, useState } from 'react'
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Pagination,
  Tabs,
  Tab,
  TableSortLabel,
} from '@mui/material'
import { Edit, Delete } from '@mui/icons-material'
import AddOrganizationDialog from './AddOrganisation'
import SearchInput from './SearchInput'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { Dispatch } from 'redux'
import {
  fetchDeleteOrgDetails,
  fetchOrganisationDetails,
  fetchRepresentativeType,
} from '../../../../actions'
import { connect } from 'react-redux'
import Loader from 'components/Common/Loader'
import { toast } from 'react-toastify'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'

interface Organization {
  id: number
  organisation: string
  city: string
  representative_type: string
  last_invited: string
}

function OrganizationsTable({
  fetchOrganisationDetails,
  OrganisationOptions,
  fetchRepresentativeType,
  RepresentativeOptions = [],
  deleteOrganisation,
  getOrganisationDetailsData,
}: any) {
  const [page, setPage] = useState(1)
  const [rowsPerPage] = useState(10)
  const [searchQuery, setSearchQuery] = useState('')
  const [openDialog, setOpenDialog] = useState(false)
  const [editingOrg, setEditingOrg] = useState<Organization | null>(null)
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [orgToDelete, setOrgToDelete] = useState<Organization | null>(null)

  useEffect(() => {
    setPage(1)
  }, [searchQuery])

  useEffect(() => {
    fetchOrganisationDetails()
    fetchRepresentativeType()
  }, [fetchOrganisationDetails, fetchRepresentativeType])

  useEffect(() => {
    if (OrganisationOptions.length > 0) {
      setOrganizations([...OrganisationOptions[0]])
    }
  }, [OrganisationOptions])

  const handleSort = () => {
    const order = sortOrder === 'asc' ? 'desc' : 'asc'
    setSortOrder(order)
    setOrganizations((prev) =>
      [...prev].sort((a, b) =>
        order === 'asc'
          ? a.organisation.localeCompare(b.organisation)
          : b.organisation.localeCompare(a.organisation),
      ),
    )
  }

  const handleEdit = (org: Organization) => {
    setEditingOrg(org)
    setOpenDialog(true)
  }

  const handleDeleteClick = (org: Organization) => {
    setOrgToDelete(org)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (orgToDelete && orgToDelete.id) {
      deleteOrganisation(orgToDelete.id)
      setOrganizations((prev) => prev.filter((org) => org.id !== orgToDelete.id))
      setOrgToDelete(null)
      toast.success('Organization deleted successfully!')
    }
    setDeleteDialogOpen(false)
  }

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value)
  }

  const filteredOrganizations = organizations.filter((org) =>
    org.organisation.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const paginatedOrganizations = filteredOrganizations.slice(
    (page - 1) * rowsPerPage,
    page * rowsPerPage,
  )

  return (
    <>
      <Loader state={!getOrganisationDetailsData} />
      <Box sx={{ p: 3, borderRadius: 1, boxShadow: 3 }}>
        <Paper sx={{ px: '2rem' }}>
          <Box className='templates-page-tabs'>
            <Tabs value={0} aria-label='Tabs for different tables'>
              <Tab label='Organization' />
            </Tabs>
          </Box>

          <Box
            display='flex'
            justifyContent='space-between'
            alignItems='center'
            marginBottom='20px'
          >
            <Box width='250px'>
              <SearchInput
                label='Search Organization'
                value={searchQuery}
                onChange={setSearchQuery}
              />
            </Box>
            <ActionButton
              onClick={() => {
                setEditingOrg(null)
                setOpenDialog(true)
              }}
            >
              Add Organization
            </ActionButton>
            <AddOrganizationDialog
              open={openDialog}
              onClose={() => setOpenDialog(false)}
              initialData={editingOrg || undefined}
              representativeOptions={RepresentativeOptions[0] || []}
              onSuccess={() => {
                fetchOrganisationDetails()
                setEditingOrg(null)
              }}
            />
          </Box>

          <TableContainer component={Paper} elevation={3} style={{ width: '100%' }}>
            <Table>
              <TableHead sx={{ backgroundColor: 'rgb(25, 60, 109)' }}>
                <TableRow>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      width: '40%',
                      fontFamily: 'Montserrat-Medium',
                    }}
                  >
                    Organization Name
                    <TableSortLabel
                      active
                      direction={sortOrder}
                      onClick={handleSort}
                      sx={{
                        color: 'white',
                        '& .MuiTableSortLabel-icon': { color: 'white !important' },
                      }}
                    ></TableSortLabel>
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      width: '30%',
                      fontFamily: 'Montserrat-Medium',
                    }}
                  >
                    City
                  </TableCell>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      width: '30%',
                      fontFamily: 'Montserrat-Medium',
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedOrganizations.length > 0 ? (
                  paginatedOrganizations.map((org, index) => (
                    <TableRow key={index}>
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {org.organisation}
                      </TableCell>
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {org.city}
                      </TableCell>
                      <TableCell align='center'>
                        <IconButton size='small' color='primary' onClick={() => handleEdit(org)}>
                          <Edit fontSize='small' />
                        </IconButton>
                        <IconButton
                          size='small'
                          color='error'
                          onClick={() => handleDeleteClick(org)}
                        >
                          <Delete fontSize='small' />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                      No matching records found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <DeleteConfirmationDialog
            open={deleteDialogOpen}
            handleClose={() => setDeleteDialogOpen(false)}
            handleConfirm={confirmDelete}
          />

          <Box display='flex' justifyContent='flex-end' marginTop='20px'>
            <Pagination
              count={Math.ceil(filteredOrganizations.length / rowsPerPage)}
              page={page}
              onChange={handlePageChange}
              color='primary'
              style={{ padding: '10px' }}
            />
          </Box>
        </Paper>
      </Box>
    </>
  )
}

const mapStateToProps = (state: RootState) => ({
  OrganisationOptions: recruitmentEntity.getRecruitment(state).getOrganisationDetailsData || [],
  RepresentativeOptions: recruitmentEntity.getRecruitment(state).getRepresentativeTypeData || [],
  getOrganisationDetailsData: recruitmentStateUI.getRecruitment(state).getOrganisationDetailsData,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchOrganisationDetails: () => dispatch(fetchOrganisationDetails.request()),
  fetchRepresentativeType: () => dispatch(fetchRepresentativeType.request()),
  deleteOrganisation: (id: number) => dispatch(fetchDeleteOrgDetails.request({ id })),
})

export default connect(mapStateToProps, mapDispatchToProps)(OrganizationsTable)
