import React, { useState } from "react";
import { Card, CardContent, Grid, Paper, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { menuItems } from "./menuData";
import MenuCard from "./MenuCard";
import SubfieldPopover from "./SubfieldPopover";
import CoolingOffPeriod from "./Recruitment/CoolingOff/CoolingOffPeriod";
import { OpenTabLogo } from "../../../utils/StaticData";
import style from "../../../utils/styles.json";
import { Diversity3 } from "@mui/icons-material";
import { RootState } from "configureStore";
import { recruitmentEntity } from "reducers";
import { fetchCoolingOffPeriod, fetchSendRemainder } from "actions";
import { Dispatch } from "redux";
import { connect } from "react-redux";
import { toast } from "react-toastify";

interface SettingProps{
  fetchSendRemainder : () => void;
  sendRemainderOPtions?: any;
  
}

const HeadingBar = {
  padding: "10px 0px",
  display: "flex",
  background: "#F7F7F7 0% 0% no-repeat padding-box",
  boxShadow: "0px 2px 3px #00000029",
  opacity: 1,
};

const welcomeMessage = {
  fontFamily: style.FONT_BOLD,
  letterSpacing: "0px",
  color: "#000000",
  opacity: "0.5",
  fontSize: "10px",
};
const DashboardText = {
  fontFamily: style.FONT_BOLD,
  letterSpacing: "0px",
  opacity: "1",
  fontWeight: "bold",
  fontSize: "14px",
};

const IconStyles: React.CSSProperties = {
  margin: "0px 10px 0 15px",
  width: "19px",
  position: "relative",
  top: "3px",
};

const cardStyles = {
  marginLeft: "18px",
  marginBottom: "18px",
  marginTop: "8px",
  width: "23%",
  minHeight: "150px",
};

const CommonCardStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "column",
  height: "100%",
};
const CardStyle = {
  padding: "16px 13px 16px 16px",
};
const CardIconStyle = {
  fontSize: 28,
  color: style.PRIMARY_COLOR,
  marginRight: "4px",
  position: "relative",
  right: "4px",
};
const CardHeadingText: React.CSSProperties = {
  position: "relative",
  bottom: "10px",
  fontFamily: style.FONT_BOLD,
};

const Settings = ({fetchSendRemainder} : SettingProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [popoverContent, setPopoverContent] = useState<any[]>([]);
  const [coolingModalOpen, setCoolingModalOpen] = useState(false);
  // const Settings: React.FC<SettingProps> = {{fetchSendRemainder, sendRemainderOptions}}

  const navigate = useNavigate();

  const handleCardClick = (
    event: React.MouseEvent<HTMLDivElement>,
    subfields: any[]
  ) => {
    setPopoverContent(subfields);
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => setAnchorEl(null);

  const handleSubfieldClick = (route: string, isModal?: boolean) => {
    if (isModal && route === "cooling-off") {
      setCoolingModalOpen(true);
      
    } 
    else if(route === 'send-reminder')
      {

        fetchSendRemainder();
        toast.success('Reminder Sent')

    }
    else {
      navigate(route);
    }
  };

  return (
    <>
      
      <div style={{ width: "100%" }}>
        <div style={{ padding: "12px" }}>
          <Typography style={welcomeMessage} variant="body1"></Typography>
          <Paper
            elevation={3}
            style={{
              width: "100%",
              height: "100%",
              padding: "15px 0px 0px 10px",
              background: "#FFFFFF 0% 0% no-repeat padding-box",
              boxShadow: "0px 3px 6px #00000029",
              opacity: "1",
              marginTop: "10px",
              overflow: "auto",
            }}
          >
            <div style={{ display: "flex", flexWrap: "wrap" }}>
              {menuItems.map((item, index) => (
                <Card sx={cardStyles} variant="outlined">
                  <div style={CommonCardStyle}>
                    <CardContent sx={CardStyle}>
                      <Typography gutterBottom>
                        <span style={{position:'fixed', width:'18%'}}>

                        <item.icon sx={CardIconStyle} />
                        <span style={CardHeadingText}> {item.label} </span>
                        </span >
                        <span style={{ float: "right" }}>
                          <img
                            src={OpenTabLogo}
                            style={{ cursor: "pointer" }}
                            alt=""
                            onClick={(e) => handleCardClick(e, item.subfields)}
                          />
                        </span>
                      </Typography>
                    </CardContent>
                    <div style={{ flexGrow: 1 }} />
                  </div>
                </Card>
              ))}
            </div>
          </Paper>
        </div>
      </div>

      <SubfieldPopover
        anchorEl={anchorEl}
        onClose={handleClosePopover}
        subfields={popoverContent}
        onSubfieldClick={(route, isModal) =>
          handleSubfieldClick(route, isModal)
        }
      />

      <CoolingOffPeriod
        open={coolingModalOpen}
        handleClose={() => setCoolingModalOpen(false)}
      />
    </>
  );
};

const mapStateToProps = (state: RootState) => ({
  sendRemainderOptions: recruitmentEntity.getRecruitment(state).getSendRemainder,

})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchSendRemainder: () => dispatch(fetchSendRemainder.request()),
})

export default connect(mapStateToProps, mapDispatchToProps)(Settings)
