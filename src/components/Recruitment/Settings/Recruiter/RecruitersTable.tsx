import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Box,
  Tabs,
  Tab,
  Pagination,
} from '@mui/material'
import { Edit, Delete, ArrowUpward, ArrowDownward } from '@mui/icons-material'
import { useEffect, useState } from 'react'
import AddRecruiterPopup from './AddRecruiterPopup'
import SearchInput from './SearchInput'
import DeleteConfirmationDialog from 'components/Recruitment/Common/DeleteConfirmationDialog'
import { connect } from 'react-redux'
import { fetchDeleteUserDetails, fetchUser } from '../../../../actions'
import { Dispatch } from 'redux'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { RootState } from '../../../../configureStore'
import Loader from 'components/Common/Loader'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { toast } from 'react-toastify'

const ITEMS_PER_PAGE = 10

interface Recruiter {
  first_name: string
  last_name: string
  email: string
  phone_no: string
  role: string
  status: string
  password: string
}

function RecruitersTable({ fetchUserData, UserOptions, deleteUser, getUser }: any) {
  const [open, setOpen] = useState(false)
  const [page, setPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [editingRecruiter, setEditingRecruiter] = useState<Recruiter | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedRecruiterId, setSelectedRecruiterId] = useState<number | null>(null)

  useEffect(() => {
    setPage(1)
  }, [searchQuery])

  useEffect(() => {
    fetchUserData()
  }, [fetchUserData])

  const handleEdit = (recruiter: Recruiter) => {
    setEditingRecruiter(recruiter)
    setOpen(true)
  }

  const handleConfirmDelete = () => {
    if (selectedRecruiterId !== null) {
      deleteUser(selectedRecruiterId)
      setTimeout(() => fetchUserData(), 300)
    }
    setDeleteDialogOpen(false)
    toast.success('Recruiter deleted successfully')
    setSelectedRecruiterId(null)
  }

  const apiRecruiters = Array.isArray(UserOptions) ? UserOptions : []
  const filteredRecruiters = apiRecruiters
    .filter(
      (recruiter) =>
        recruiter.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recruiter.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recruiter.phone_no.includes(searchQuery),
    )
    .sort((a, b) =>
      sortOrder === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name),
    )

  const displayedRecruiters = filteredRecruiters.slice(
    (page - 1) * ITEMS_PER_PAGE,
    page * ITEMS_PER_PAGE,
  )

  const toggleSortOrder = () => {
    setSortOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'))
  }

  return (
    <>
      <Loader state={!getUser} />
      <Box sx={{ width: '98%', height: '100%', padding: '20px' }}>
        <Paper sx={{ px: '2rem' }}>
          <Box className='templates-page-tabs'>
            <Tabs value={0} aria-label='Tabs for different tables'>
              <Tab label='Recruiters' />
            </Tabs>
          </Box>
          <Box
            display='flex'
            justifyContent='space-between'
            alignItems='center'
            marginBottom='20px'
          >
            <Box width='230px'>
              <SearchInput
                label='Search Recruiters'
                value={searchQuery}
                onChange={setSearchQuery}
              />
            </Box>
            <ActionButton
              onClick={() => {
                setEditingRecruiter(null)
                setOpen(true)
              }}
            >
              Add Recruiter
            </ActionButton>
            <AddRecruiterPopup
              open={open}
              onClose={() => setOpen(false)}
              initialData={editingRecruiter || undefined}
              refreshRecruiters={fetchUserData}
            />
          </Box>

          <TableContainer component={Paper} elevation={3} sx={{ width: '100%' }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      padding: '10px',
                      backgroundColor: 'rgb(25, 60, 109)',
                      textAlign: 'center',
                      cursor: 'pointer',
                      fontFamily: 'Montserrat-Medium',
                    }}
                    onClick={toggleSortOrder}
                  >
                    Name
                    <IconButton size='small' sx={{ color: 'white', marginLeft: '5px' }}>
                      {sortOrder === 'asc' ? <ArrowUpward /> : <ArrowDownward />}
                    </IconButton>
                  </TableCell>
                  {['Email', 'Phone No.', 'Status', 'Actions'].map((heading, index) => (
                    <TableCell
                      key={index}
                      sx={{
                        width: heading === 'Actions' ? '15%' : '20%',
                        color: 'white',
                        fontFamily: 'Montserrat-Medium',
                        fontWeight: 'bold',
                        padding: '16px',
                        backgroundColor: 'rgb(25, 60, 109)',
                        textAlign: 'center',
                      }}
                    >
                      {heading}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {displayedRecruiters.length > 0 ? (
                  displayedRecruiters.map((recruiter, index) => (
                    <TableRow
                      key={index}
                      sx={{ backgroundColor: index % 2 === 0 ? '#ffffff' : '#f8f9fa' }}
                    >
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {recruiter.name}
                      </TableCell>
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {recruiter.email}
                      </TableCell>
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {recruiter.phone_no}
                      </TableCell>
                      <TableCell align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                        {recruiter.status}
                      </TableCell>
                      <TableCell align='center'>
                        <IconButton
                          size='small'
                          color='primary'
                          onClick={() => handleEdit(recruiter)}
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size='small'
                          sx={{ color: '#e53935' }}
                          onClick={() => {
                            setSelectedRecruiterId(recruiter.id)
                            setDeleteDialogOpen(true)
                          }}
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align='center' sx={{ fontFamily: 'Montserrat-Medium' }}>
                      No matching records found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <Box style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
            <Pagination
              count={Math.ceil(filteredRecruiters.length / ITEMS_PER_PAGE)}
              page={page}
              onChange={(_, value) => setPage(value)}
              color='primary'
              style={{ padding: '10px' }}
            />
          </Box>
        </Paper>
        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          handleClose={() => setDeleteDialogOpen(false)}
          handleConfirm={handleConfirmDelete}
        />
      </Box>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    UserOptions: recruitmentEntity.getRecruitment(state).getUser,
    getUser: recruitmentStateUI.getRecruitment(state).getUser,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchUserData: () => dispatch(fetchUser.request({})),
    deleteUser: (id: number) => dispatch(fetchDeleteUserDetails.request({ id })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(RecruitersTable)
