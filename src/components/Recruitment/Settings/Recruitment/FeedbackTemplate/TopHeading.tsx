import React from 'react'
import { Box, Tabs, Tab } from '@mui/material'

const TopHeading: React.FC = () => {
  return (
    
      <Tabs
        value={0}
        aria-label='Tabs for different tables'
        sx={{ display: 'flex', flexDirection: 'row', }}
      >
        <Tab
          label='Feedback Template'
          sx={{
            backgroundColor: 'rgba(0, 0, 0, 0)',
            fontWeight: '800',
            fontSize: '20px',
            fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
            paddingBottom: 1,
            color: 'rgb(25, 60, 109)',
            '&.Mui-selected': {
              color: 'rgb(25, 60, 109)',
              borderBottom: '2px solid rgb(25, 60, 109)',
            },
            '&:hover': {
              color: 'rgb(25, 60, 109)',
              borderBottom: '2px solid rgb(25, 60, 109)',
            },
            '& .MuiTouchRipple-root': {
              display: 'none',
            },
          }}
        />
      </Tabs>
   
  )
}

export default TopHeading
