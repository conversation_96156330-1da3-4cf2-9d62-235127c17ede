import { useState } from 'react'
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField } from '@mui/material'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { fetchCoolingOffPeriod } from 'actions'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { toast } from 'react-toastify'




interface CoolingOffPeriodModalProps {
  open: boolean
  handleClose: () => void
  coolingOffPeriodOptions: () => any
  fetchCoolingOffPeriod: () => void 
}

const CoolingOffPeriod = ({ open, handleClose,fetchCoolingOffPeriod  }: CoolingOffPeriodModalProps) => {
  const [value, setValue] = useState<string>('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    if (/^\d*$/.test(inputValue)) {
      setValue(inputValue)
    }
  }
  const handleSave = ()=>{
    fetchCoolingOffPeriod()
    handleClose()
    toast.success("Month Updated Successfully!")
    

  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth='sm' fullWidth>
      <DialogTitle
        sx={{
          bgcolor: '#193C6D',
          color: '#fff',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        Cooling Off Period
      </DialogTitle>

      <DialogContent sx={{ padding: '20px' }}>
        <TextField
          fullWidth
          variant='outlined'
          value={value}
          onChange={handleChange}
          inputProps={{ min: 0 }}
        />
      </DialogContent>

      <DialogActions sx={{ padding: '16px' }}>
        <ActionButton onClick={handleClose} variant='outlined'>
          Cancel
        </ActionButton>
        <ActionButton
          onClick={handleSave} 
         variant='contained'
          sx={{
            bgcolor: value ? '#193C6D' : '#4d4d4d',
            color: '#fff',
            '&:hover': { bgcolor: value ? '#142c52' : '#4d4d4d' },
          }}
          disabled={!value}
        >
          Update
        </ActionButton>
      </DialogActions>
    </Dialog>
  )
}

const mapStateToProps = (state: RootState) => ({
  coolingOffPeriodOptions: recruitmentEntity.getRecruitment(state).getCoolingOffPeriod,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchCoolingOffPeriod: () => dispatch(fetchCoolingOffPeriod.request())
})

export default connect(mapStateToProps, mapDispatchToProps)(CoolingOffPeriod)

