import { useState, MouseEvent } from 'react'
import { Menu, MenuItem, Checkbox, ListItemText, TextField } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import { ActionButton } from '../../../HolidayList/HolidaysStyles'

interface Option {
  label: string
  value: string | number
}

interface MultiSelectDropdownProps {
  label: string
  options: Option[]
  selectedOptions: (string | number)[]
  setSelectedOptions: (selected: (string | number)[]) => void
  sx?: object
}

export default function MultiSelectDropdown({
  label,
  options,
  selectedOptions,
  setSelectedOptions,
  sx
}: MultiSelectDropdownProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [searchText, setSearchText] = useState<string>('')

  const open = Boolean(anchorEl)

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleToggleOption = (option: Option) => {
    setSelectedOptions(
      selectedOptions.includes(option.value)
        ? selectedOptions.filter((item) => item !== option.value)
        : [...selectedOptions, option.value],
    )
  }

  const handleSelectAll = () => {
    setSelectedOptions(
      selectedOptions.length === options.length ? [] : options.map((option) => option.value),
    )
  }

  const filteredOptions = options.filter(
    (option) =>
      typeof option.label === 'string' &&
      option.label.toLowerCase().includes(searchText.toLowerCase()),
  )

  return (
    <div>
      <ActionButton
        variant='outlined'
        onClick={handleClick}
        sx={{ justifyContent: 'space-between', ...sx }}
        fullWidth
      >
        {label} {selectedOptions.length > 0 ? `+${selectedOptions.length}` : ''}
        <ArrowDropDownIcon />
      </ActionButton>

      <Menu anchorEl={anchorEl} open={open} onClose={handleClose} sx={{ maxHeight: '400px',maxWidth:'500px',position:'absolute' }}>
        <MenuItem onClick={handleSelectAll}>
          <Checkbox checked={selectedOptions.length === options.length} />
          {selectedOptions.length < options.length ? 'Select All' : 'Un-Select All'}
        </MenuItem>

        <MenuItem onKeyDown={(e) => e.stopPropagation()}>
          <TextField
            fullWidth
            variant='standard'
            placeholder='Search'
            onChange={(e) => setSearchText(e.target.value)}
          />
        </MenuItem>

        {filteredOptions.map((option) => (
          <MenuItem key={option.value} onClick={() => handleToggleOption(option)}>
            <Checkbox checked={selectedOptions.includes(option.value)} />
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </Menu>
    </div>
  )
}
