import React from 'react'
import { IconButton, Tooltip } from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { useNavigate } from 'react-router-dom'
import type { SxProps, Theme } from '@mui/system'

interface BackButtonProps {
  steps?: number
  tooltip?: string
  sx?: SxProps<Theme>
}

const BackButton: React.FC<BackButtonProps> = ({ steps = -1, tooltip, sx }) => {
  const navigate = useNavigate()

  const button = (
    <IconButton
      onClick={() => navigate(steps)}
      sx={{
        backgroundColor: 'rgb(25, 60, 109)',
        color: '#fff',
        '&:hover': {
          backgroundColor: 'rgb(20, 50, 90)',
        },
        ...sx,
      }}
    >
      <ArrowBackIcon />
    </IconButton>
  )

  return tooltip ? <Tooltip title={tooltip}>{button}</Tooltip> : button
}

export default BackButton
