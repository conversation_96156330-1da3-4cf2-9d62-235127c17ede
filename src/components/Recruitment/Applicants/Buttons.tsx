import { Box, Stack, Dialog, DialogTitle, DialogContent, DialogActions, Typography } from '@mui/material'
import { ActionButton } from '../../HolidayList/HolidaysStyles'
import { useState } from 'react'
import { toast, ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import { getApprovedApplicants, getRejectedApplicants } from 'services'
import { approveApplicants, deleteApplicants, rejectApplicants } from 'actions'
import { RootState } from 'configureStore'
import { Dispatch } from 'redux'
import { connect } from 'react-redux/es/exports'
import { recruitmentEntity, recruitmentStateUI} from 'reducers'
import { useApplicantContext } from './ApplicantsContext'
import { IdApplicants } from 'sagas/Types'

const Buttons = ({ fetchSpamingData, isRowSelected, fetchApprovedData, fetchRejectedData, fetchMultipleDelete, MultipleDeleteApplicants,clearSelectedRows }: any) => {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [actionType, setActionType] = useState('')

  const handleFetchData = () => {
    fetchSpamingData()
  }

  const {state} = useApplicantContext()

  const handleActionClick = (action: string) => {
    setActionType(action)
    setDialogOpen(true)
  }

  

  const handleConfirmAction = () => {
    setDialogOpen(false)
  
    if (actionType === 'approve') {
      const payload = {
        id: state.id,
        emailList: state.emailList,
        status: "Active",
        tags: state.tags,
        experience: state.experience,
        position: state.position,
        qualification: state.qualification, 
        subject: state.subject,
        phone_no: null
      }
      fetchApprovedData(payload)
      
  
    } 
    else if (actionType === 'reject') {
      
      fetchRejectedData({}) 
    } 
    else if (actionType === 'delete') {
      fetchMultipleDelete(state.id.map((i: any) => ({ id: i }))
    [MultipleDeleteApplicants])
    }
    
  clearSelectedRows()
    toast.success(
      `${actionType.charAt(0).toUpperCase() + actionType.slice(1)} action successful!`,
      {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: 'colored',
      },
    )
  }

  console.log("state",state.id)
  
  return (
    <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'flex-end' }}>
        <Stack direction='row' spacing={2}>
          <ActionButton variant='contained' color='primary' onClick={handleFetchData}>
            Suspected Spam
          </ActionButton>
          <ActionButton
            variant='contained'
            disabled={!isRowSelected}
            onClick={() => handleActionClick('approve')}
          
          >
            Approve
          </ActionButton>
          <ActionButton
            variant='contained'
            disabled={!isRowSelected}
            onClick={() => handleActionClick('reject')}
          >
            Reject
          </ActionButton>
          <ActionButton
            variant='contained'
            disabled={!isRowSelected}
            onClick={() => handleActionClick('delete')}
          >
            Delete
          </ActionButton>
        </Stack>
      </Box>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle  sx={{
          bgcolor: 'rgb(25, 60, 109)',
          color: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '1rem',
          marginBottom: '1rem',
          fontFamily: 'Montserrat-Medium, sans-serif',
          fontWeight: 'bold',
          fontSize:"16px"
        }}>Confirm {actionType}</DialogTitle>
        <DialogContent sx={{ padding: '20px', fontFamily: 'Montserrat-Medium, sans-serif', paddingBottom: '0rem' }}>
          
          <Typography sx={{ fontFamily: 'Montserrat-Medium, sans-serif', fontSize:"13px" }}>
          Are you sure you want to {actionType} the selected applicants?
        </Typography>
        </DialogContent>
        <DialogActions>
          <ActionButton onClick={() => setDialogOpen(false)} color='secondary'>
            Cancel
          </ActionButton>
          <ActionButton onClick={handleConfirmAction} color='primary'>
            Confirm
          </ActionButton>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </Box>
  )
}

const mapStateToProps = (state: RootState) => ({
  ApprovedOptons: recruitmentEntity.getRecruitment(state).getApproveApplicants,
  RejectedOptions: recruitmentEntity.getRecruitment(state).getRejectedApplicants,
  MultipleDeleteApplicantsOptions: recruitmentStateUI.getRecruitment(state).getMultipleDeleteApplicants,
  
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  fetchApprovedData: (data: {}) => dispatch(approveApplicants.request({ data })),
  fetchRejectedData: (data: {}) => dispatch(rejectApplicants.request({ data })),
  fetchMultipleDelete: (data:IdApplicants[]) => dispatch(deleteApplicants.request(data)),

})

export default connect(mapStateToProps, mapDispatchToProps)(Buttons)