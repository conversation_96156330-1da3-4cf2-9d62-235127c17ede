import * as React from 'react'
import Grid from '@mui/material/Grid'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import Select, { SelectChangeEvent } from '@mui/material/Select'
import { useEffect} from 'react'

import {
  dropdownLabels,
  initialValues,
} from './utils/ApplicantDropUtils'
import { RootState } from '../../../configureStore'
import { recruitmentEntity } from '../../../reducers'
import { Dispatch } from 'redux'
import { fetchPositions, fetchTags, getQualification } from '../../../actions'
import { connect } from 'react-redux'
import { useApplicantContext } from './ApplicantsContext'

function ApplicantsDrop(props: any) {
  const {
    positionOptions,
    tagOptions,
    qualificationOptions,
    fetchQualificationData,
    fetchPositionsData,
    fetchTagsData,
  } = props

  useEffect(() => {
    fetchPositionsData()
    fetchTagsData()
    fetchQualificationData()
  }, [])

  const { state, dispatch } = useApplicantContext()
  interface Option {
    label: string
    value: string | number
  }

  const [values, setValues] = React.useState(initialValues)
  // const [selectedOptions, setSelectedOptions] = useState<(string | number)[]>([])
  const handleChange = (event: SelectChangeEvent, box: string) => {
    const value = event.target.value as string

    setValues((prev: any) => ({
      ...prev,
      [box]: value,
    }))

    switch (box) {
      case 'box1':
        dispatch({ type: 'FETCH_UNAPPROVED_POSITION', payload: value })
        break
      case 'box2':
        dispatch({ type: 'FETCH_UNAPPROVED_EXPERIENCE', payload: value })
        break
      case 'box3':
        dispatch({ type: 'FETCH_UNAPPROVED_QUALIFICATION', payload: value })
        break
      case 'box4':
        dispatch({ type: 'FETCH_UNAPPROVED_SUBJECTS', payload: value })
        break
      case 'box5':
        dispatch({ type: 'FETCH_UNAPPROVED_TAGS', payload: value })
        break
      default:
        break
    }
  }

  const dropdownOptions = {
    box1:
      positionOptions[1]?.map((pos: { id: string; name: string }) => ({
        id: pos.id,
        name: pos.name,
      })) || [],
    box2: (positionOptions[0] || [])
      .sort((a: { experience: string }, b: { experience: string }) => {
        const numA = parseInt(a.experience, 10)
        const numB = parseInt(b.experience, 10)

        if (isNaN(numA) || isNaN(numB)) {
          return a.experience.localeCompare(b.experience, undefined, {
            numeric: true,
            sensitivity: 'base',
          })
        }

        return numA - numB
      })
      .map((pos: { id: string; experience: string }) => ({
        id: pos.id,
        name: pos.experience,
      })),
    box3:
      qualificationOptions[0]?.map((qual: { id: string; qualification: string }) => ({
        id: qual.id,
        name: qual.qualification,
      })) || [],
    box4: [
      { id: 'Arts', name: 'Arts' },
      { id: 'Commerce', name: 'Commerce' },
      { id: 'Diploma', name: 'Diploma' },
      { id: 'Other', name: 'Other' },
      { id: 'PCB', name: 'PCB' },
      { id: 'PCM', name: 'PCM' },
    ],
    box5:
      tagOptions[0]?.map((tag: { id: string; name: string }) => ({ id: tag.id, name: tag.name })) ||
      [],
  }

  return (
    <>
      <Grid
        container
        spacing={2}
        alignItems='center'
        justifyContent='space-evenly'
        sx={{ borderRadius: 2 }}
      >
        {Object.keys(dropdownOptions).map((box, index) => (
          <Grid item xs={12} sm={6} md={2.4} lg={2.4} key={box}>
            <FormControl fullWidth>
              <InputLabel sx={{ mt: '10px' }}>{dropdownLabels[index]}</InputLabel>
              <Select
                value={values[box as keyof typeof values]}
                label={dropdownLabels[index]}
                onChange={(event) => handleChange(event, box)}
                sx={{ borderRadius: '40px', height: '45px', marginTop: '15px'}}
                MenuProps={{
                  PaperProps:{
                    sx:{
                      width:'auto',
                      
                    },
                  },
                }}
              >
                
                {dropdownOptions[box as keyof typeof dropdownOptions].map(
                  (option: { id: string | number; name: string }) => (
                    <MenuItem key={option.id} value={option.id} >
                      {option.name}
                    </MenuItem>
                  ),
                )}
              </Select>
            </FormControl>
          </Grid>
        ))}
      </Grid>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    positionOptions: recruitmentEntity.getRecruitment(state).getPositionData,
    tagOptions: recruitmentEntity.getRecruitment(state).getTagData,
    qualificationOptions: recruitmentEntity.getRecruitment(state).getAllQualification,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchPositionsData: () => dispatch(fetchPositions.request()),
    fetchTagsData: () => dispatch(fetchTags.request()),
    fetchQualificationData: () => dispatch(getQualification.request()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ApplicantsDrop)