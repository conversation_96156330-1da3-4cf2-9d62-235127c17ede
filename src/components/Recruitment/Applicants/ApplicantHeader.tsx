import React from 'react'
import {
  Box,
  Collapse,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  Stack,
  Tab,
  Tabs,
} from '@mui/material'
import FilterListIcon from '@mui/icons-material/FilterList'

import Applicants from './ApplicantsDrop'
import { SearchBoxCustom } from '../../Common/CommonStyles'
import { SearchIconStyle } from '../../Common/CommonStyles'
import ApplicantsDrop from './ApplicantsDrop'
import { Close as CloseIcon, Search as SearchIcon } from '@mui/icons-material/'

interface ApplicantHeaderProps {
  searchQuery: string
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>
}

const ApplicantHeader = ({ searchQuery, setSearchQuery }: ApplicantHeaderProps) => {
  const [showApplicants, setShowApplicants] = React.useState<boolean>(false)

  return (
    <Paper
      sx={{
        position: 'sticky',
        top: 0,
        zIndex: 100,
        p: 2,
        boxShadow: 3,
        backgroundColor: '#fff',
      }}
    >
      <Box className='templates-page-tabs'>
        <Tabs value={0} aria-label='Tabs for different tables'>
          <Tab label='Applicants' />
        </Tabs>
      </Box>

      <Box display={'flex'} alignItems={'center'} justifyContent={'flex-start'} my={'15px'}>
        <Box width='calc(50% - 100px)'>
          <SearchBoxCustom
            id='outlined-basic'
            placeholder='Search'
            variant='outlined'
            size='small'
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ width: '250px', margin: '0' }}
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: searchQuery && (
                <InputAdornment position='end'>
                  <IconButton size='small' onClick={() => setSearchQuery('')} edge='end'>
                    <CloseIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>
      </Box>

      <ApplicantsDrop />

      <Box sx={{ marginTop: 2 }}>
        <Collapse in={showApplicants}>
          <Applicants />
        </Collapse>
      </Box>
    </Paper>
  )
}

export default ApplicantHeader