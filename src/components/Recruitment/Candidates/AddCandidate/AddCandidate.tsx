import React, { useState, useCallback, useEffect, ChangeEvent } from 'react';
import { Typography, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material/Select';
import dayjs, { Dayjs } from 'dayjs';
import PrimaryDetails from './PrimaryDetails';
import AdditionalDetails from './AdditionalDetails';
import StatusTags from './StatusTags';
import Buttons from './Buttons';
import { connect } from 'react-redux';
import { Dispatch } from 'redux';
import { RootState } from '../../../../configureStore';
import { toast } from 'react-toastify';
import { addCandidate, addResume, fetchAddQualification, fetchPositions, fetchTags } from '../../../../actions';
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers';
import Loader from 'components/Common/Loader';

interface addcandidateform {
    created_by: number
    firstName: string
    lastName: string
    email: string
    contact: number
    highestQualification: string
    subject: string
    position: string
    experience: string
    status: string
    dob: any
    tags?: any[]
    resume: ''
}
function AddCandidate(props: any) {
    const [formData, setFormData] = useState({
        created_by: 1451,
        firstName: '',
        lastName: '',
        email: '',
        contact: '',
        highestQualification: '',
        subject: '',
        position: '',
        experience: '',
        status: 'Active',
        dob: null as Dayjs | null,
        tags: '',
        resume: null as File | null,
        resumeURL: null as string | null,
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isFormValid, setIsFormValid] = useState(false);
    const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});

    const {
        fetchAddQualification,
        AddQualificationOptions,
        fetchTag,
        TagOptions,
        fetchPosition,
        PositionOptions,
        addCandidate,
        CandidateRespose,
        isCandidateAdded,
        resetAddCandidate,
        addResume,
        viewResume,
        isAddResume,
    } = props;
    const [dynamicTagOptions, setDynamicTagOptions] = useState<string[]>([]);
    const [qualificationOptions, setQualificationOptions] = useState<{ id: string, name: string }[]>([]);
    const [tags, setTags] = useState<string[]>([]);

    const navigate = useNavigate();
    const [experienceOptions, setExperienceOptions] = useState<{ id: string, name: string }[]>([]);
    const [positionOptions, setPositionOptions] = useState<{ id: string, name: string }[]>([]);
    const subjectOptions = ['Commerce', 'Arts', 'PCM', 'PCB', 'Diploma', 'Other'];
    const statusOptions = ['Active', 'Inactive'];

    const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        if (file) {
            if (file.type !== 'application/pdf') {
                setErrors((prev) => ({ ...prev, resume: 'Only PDF files are allowed.' }));
                return;
            }
            if (file.size > 5 * 1024 * 1024) {
                setErrors((prev) => ({ ...prev, resume: 'File size should not exceed 5MB.' }));
                return;
            }
            setErrors((prev) => ({ ...prev, resume: '' }));

            addResume({ file });

            setFormData((prev) => ({
                ...prev,
                resume: file,
                resumeURL: URL.createObjectURL(file),
            }));
        }
        e.target.value = '';
    };
    const handleResumeRemove = () => {
        setFormData((prev) => ({ ...prev, resume: null, resumeURL: '' }));
        setErrors((prev) => ({ ...prev, resume: '' }));
    };
    const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>) => {
        if ('target' in e) {
            const { name, value } = e.target;

            if (name === 'contact') {
                const numericValue = value.replace(/\D/g, '').slice(0, 10);
                setFormData(prev => ({ ...prev, contact: numericValue }));
                setTouchedFields(prev => ({ ...prev, contact: true }));
                return;
            }

            setFormData(prev => ({ ...prev, [name]: value }));

            if (name === 'email') {
                setTouchedFields(prev => ({ ...prev, email: true }));
            }
        }
    };
    const handleDateChange = (date: Dayjs | null) => {
        setFormData(prev => ({ ...prev, dob: date }));
        setTouchedFields(prev => ({ ...prev, dob: true }));
    };
    const handleSubmit = (e: { preventDefault: () => void }) => {
        e.preventDefault();
        validateForm();

        const formattedData = {
            created_by: formData.created_by,
            first_name: formData.firstName.trim(),
            last_name: formData.lastName.trim(),
            email: formData.email.trim(),
            phone_no: formData.contact.trim(),
            status: formData.status,
            qualification: formData.highestQualification,
            position: formData.position,
            experience: formData.experience,
            dob: formData.dob ? dayjs(formData.dob).format("DD-MM-YYYY") : null,
            subjects: formData.subject.trim(),
            tag: tags.length > 0 ? tags : [],
            resume: props.viewResume || '',
        };
        addCandidate(formattedData);
    };

    const validateForm = useCallback(() => {
        const newErrors: Record<string, string> = {};

        if (touchedFields.email) {
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
                newErrors.email = 'Invalid email';
            }
        }
        if (touchedFields.dob) {
            if (!formData.dob || !dayjs(formData.dob).isValid()) {
                newErrors.dob = 'Invalid date of birth';
            } else {
                const eighteenYearsAgo = dayjs().subtract(18, 'year');
                if (dayjs(formData.dob).isAfter(eighteenYearsAgo)) {
                    newErrors.dob = 'Must be 18+ years';
                }
            }
        }
        if (touchedFields.contact) {
            if (!/^\d{10}$/.test(formData.contact)) {
                newErrors.contact = 'Contact number must be 10 digits';
            }
        }
        setErrors(newErrors);
        const isFormValid =
            !Object.keys(newErrors).length &&
            !!formData.firstName.trim() &&
            !!formData.lastName.trim() &&
            !!formData.email.trim() &&
            formData.contact.length === 10 &&
            !!formData.highestQualification &&
            !!formData.subject &&
            !!formData.position &&
            !!formData.experience &&
            !!formData.status &&
            !!formData.dob;

        setIsFormValid(isFormValid);
    }, [formData, touchedFields, setErrors]);

    useEffect(() => {
        let str = viewResume
        if (str) {
            setFormData((prev) => ({
                ...prev,
                resumeURL: str,
            }));
        }
    }, [props.viewResume]);

    useEffect(() => {
        if (!CandidateRespose) return;
        if (isCandidateAdded && CandidateRespose.code === -123) {
            toast.error(CandidateRespose.message);
            resetAddCandidate();
            return;
        }
        if (isCandidateAdded && CandidateRespose.code === -103) {
            toast.success("Candidate Successfully Added");
            resetAddCandidate();
            navigate(-1);
        }
    }, [isCandidateAdded]);

    useEffect(() => {
        fetchAddQualification();
        fetchTag();
        fetchPosition();
        validateForm();
    }, [formData, validateForm]);

    useEffect(() => {
        if (AddQualificationOptions?.length > 0) {
            setQualificationOptions(AddQualificationOptions[0].map((item: any) => ({ name: item.qualification, id: item.id })));
        }
        if (TagOptions?.length > 0) {
            setDynamicTagOptions(TagOptions[0].map((tagObj: any) => tagObj.name));
        }
        if (PositionOptions?.length > 1) {
            setExperienceOptions(
                PositionOptions[0].map((expObj: any) => ({
                    id: expObj.id,
                    name: expObj.experience,
                }))
            );
            setPositionOptions(
                PositionOptions[1].map((posObj: any) => ({
                    id: posObj.id,
                    name: posObj.name,
                }))
            );
        }
    }, [AddQualificationOptions, TagOptions, PositionOptions]);

    return (
        <>
            <Loader state={isAddResume} />
            <Box
                component='form'
                onSubmit={handleSubmit}
                sx={{
                    display: 'flex',
                    fontFamily: "Montserrat-Medium",
                    flexWrap: 'wrap',
                    gap: 2,
                    padding: '24px',
                    maxWidth: '75%',
                    margin: 'auto',
                    backgroundColor: '#fff',
                    marginTop: '40px',
                    marginBottom: '20px',
                }}
            >
                <Typography
                    variant='h4'
                    color='black'
                    sx={{ textAlign: 'center', margin: 'auto' }}
                >
                    Add Candidate
                </Typography>
                <PrimaryDetails
                    formData={formData}
                    errors={errors}
                    onChange={handleChange}
                    qualificationOptions={qualificationOptions}
                    subjectOptions={subjectOptions}
                />
                <AdditionalDetails
                    formData={formData}
                    errors={errors}
                    onChange={handleChange}
                    onDateChange={handleDateChange}
                    positionOptions={positionOptions}
                    experienceOptions={experienceOptions}
                />
                <StatusTags
                    formData={formData}
                    errors={errors}
                    onChange={handleChange}
                    statusOptions={statusOptions}
                    tags={tags}
                    onTagsChange={setTags}
                    tagOptions={dynamicTagOptions}
                />
                <Buttons
                    resume={formData.resume}
                    onUpload={handleFileUpload}
                    onRemove={handleResumeRemove}
                    onGoBack={() => navigate(`/home/<USER>
                    isFormValid={isFormValid && Object.keys(errors).length === 0}
                    onSubmit={(e) => { handleSubmit(e); }}
                    error={errors.resume}
                />
            </Box>
        </>
    )
}

const mapStateToProps = (state: RootState) => ({
    AddQualificationOptions: recruitmentEntity.getRecruitment(state).getAddQualificationData,
    TagOptions: recruitmentEntity.getRecruitment(state).getTagData,
    PositionOptions: recruitmentEntity.getRecruitment(state).getPositionData,
    CandidateRespose: recruitmentEntity.getRecruitment(state).addCandidate,
    isCandidateAdded: recruitmentStateUI.getRecruitment(state).isCandidateAdded,
    isAddResume: recruitmentStateUI.getRecruitment(state).isAddResume,
    viewResume: recruitmentEntity.getRecruitment(state).addResume
})
const mapDispatchToProps = (dispatch: Dispatch) => ({
    fetchAddQualification: () => dispatch(fetchAddQualification.request()),
    fetchTag: () => dispatch(fetchTags.request()),
    addResume: (data: any) => dispatch(addResume.request(data)),
    fetchPosition: () => dispatch(fetchPositions.request()),
    resetAddCandidate: () => dispatch(addCandidate.reset()),
    addCandidate: (payload: addcandidateform) => dispatch(addCandidate.request(payload)),
})
export default connect(mapStateToProps, mapDispatchToProps)(AddCandidate)