import React, { useState } from 'react'
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Box,
  SelectChangeEvent,
  TextField,
} from '@mui/material'
import Autocomplete from '@mui/material/Autocomplete'
import Chip from '@mui/material/Chip'

interface StatusTagsProps {
  formData: {
    status: string
  }
  errors: {
    status?: string
  }
  onChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>,
  ) => void
  statusOptions: string[]
  tags: string[]
  onTagsChange: (tags: string[]) => void
  tagOptions: string[]
}
const StatusTags: React.FC<StatusTagsProps> = ({
  formData,
  errors,
  onChange,
  statusOptions,
  tags,
  onTagsChange,
  tagOptions,
}) => {
  const [inputValue, setInputValue] = useState('')
  const [open, setOpen] = useState(false)

  const filterOptions = (options: string[], { inputValue }: { inputValue: string }) => {
    return options.filter((option) => option.toLowerCase().includes(inputValue.toLowerCase()))
  }

  const handleInputChange = (_event: React.SyntheticEvent, newInputValue: string) => {
    setInputValue(newInputValue)
    setOpen(newInputValue.trim() !== '')
  }
  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', sm: 'repeat(3, 1fr)' },
        gap: 2,
        width: '100%',
        alignItems: 'center',
      }}
    >
      <FormControl
        required
        size='small'
        sx={{
          width: '100%',
          minWidth: '250px',
          maxWidth: '559.67px',
          height: '56px',
          '& .MuiInputBase-root': {
            height: '56px',
            paddingLeft: '8px',
            paddingRight: '8px',
          },
          '& .MuiFormLabel-asterisk': {
            color: 'red',
          },
          '& .MuiOutlinedInput-root': { borderRadius: '50px' },
          '& .MuiInputLabel-root': {
            top: '14px',
            left: '16px',
            transform: 'translateY(0)',
          },
          '& .MuiInputLabel-shrink': {
            top: '-5px',
            left: '16px',
            transform: 'scale(0.75)',
          },
        }}
      >
        <InputLabel>Status</InputLabel>
        <Select name='status' value={formData.status} onChange={onChange} label='Status'>
          {statusOptions.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <Box
        sx={{
          gridColumn: 'span 1',
          display: 'flex',
          alignItems: 'center',
          maxWidth: '100%',
        }}
      >
        <Autocomplete
          fullWidth
          multiple
          freeSolo
          options={tagOptions}
          value={tags}
          onChange={(_, newValue) => onTagsChange(newValue)}
          onInputChange={handleInputChange}
          filterOptions={filterOptions}
          open={open}
          onClose={() => setOpen(false)}
          getOptionLabel={(option) => option || ''}
          renderTags={(value, getTagProps) => (
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'nowrap',
                overflowX: 'auto',
                gap: 0.5,
                py: 1,
                maxWidth: '100%',
                '&::-webkit-scrollbar': {
                  height: '4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#193C6D',
                  borderRadius: '2px',
                },
              }}
            >
              {value.map((option, index) => (
                <Chip
                  variant='outlined'
                  label={option}
                  {...getTagProps({ index })}
                  key={index}
                  sx={{
                    fontFamily: 'Montserrat-Medium',
                    fontSize: '14px',
                    flexShrink: 0,
                  }}
                />
              ))}
            </Box>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder='Enter a new tag'
              variant='standard'
              sx={{
                fontFamily: 'Montserrat-Medium',
                '& .MuiInputBase-root': {
                  flexWrap: 'nowrap',
                  gap: 0.5,
                  minWidth: '250px',
                  maxWidth: '559.67px',
                  overflow: 'hidden',
                  fontFamily: 'Montserrat-Medium',
                },
                '& .MuiInput-underline:before': {
                  borderBottomColor: '#193C6D',
                },
                '& .MuiInput-underline:hover:not(.Mui-disabled):before': {
                  borderBottomColor: '#193C6D',
                  borderBottomWidth: '3px',
                },
                '& .MuiInput-underline:after': {
                  borderBottomColor: '#193C6D',
                },
              }}
            />
          )}
          sx={{
            fontFamily: 'Montserrat-Medium',
            '& .MuiAutocomplete-inputRoot': {
              fontFamily: 'Montserrat-Medium',
              padding: '6px 0 8px',
              flexWrap: 'nowrap',
            },
            '& .MuiAutocomplete-popupIndicator': {
              color: '#193C6D',
            },
            '& .MuiAutocomplete-option': {
              fontFamily: 'Montserrat-Medium',
              backgroundColor: '#f5f5f5',
              borderRadius: '4px',
              margin: '4px',
              '&:hover': {
                backgroundColor: '#e0e0e0',
              },
            },
          }}
        />
      </Box>
    </Box>
  )
}

export default StatusTags
