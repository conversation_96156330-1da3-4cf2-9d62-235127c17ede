import React, { useState, useEffect, useCallback } from 'react'
import { Dialog, DialogTitle, DialogContent, Grid, TextField, Box } from '@mui/material'
import { ActionButton } from 'components/HolidayList/HolidaysStyles'
import { EditRowFieldMappings } from './utils'
import { EditRowProps } from './types'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { connect } from 'react-redux'
import { dataPayloadType } from 'actions/Types'
import { editCandidateInline } from 'actions'

const EditRow: React.FC<EditRowProps> = ({
  isOpen,
  onClose,
  formData,
  editCandidateInline,
  isEditCandidateInline,
}) => {
  const [modalData, setModalData] = useState({ ...formData });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isFormValid, setIsFormValid] = useState(false);

  const extractMarks = useCallback((feedback: string | null) => {
    const match = feedback?.match(/^(\d+)\/(\d+)$/);
    return match
      ? { marks_obtained: match[1], marks_total: match[2] }
      : { marks_obtained: '', marks_total: '' };
  }, []);

  const validateField = useCallback((name: string, value: string) => {
    let error = '';
    if (value.trim() === '') {
      error = 'This field is required';
    } else if (name === 'email' && !/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(value)) {
      error = 'Invalid email format';
    } else if (name === 'phone_no' && !/^\d{10}$/.test(value)) {
      error = 'Phone number must be 10 digits';
    } else if ((name === 'marks_obtained' || name === 'marks_total') && isNaN(Number(value))) {
      error = 'Marks should be a number';
    }
    setErrors((prevErrors) => ({ ...prevErrors, [name]: error }));
    return error === '';
  }, []);

  const validateForm = useCallback((data: typeof modalData) => {
    let formValid = true;
    const fieldErrors: { [key: string]: string } = {};
    EditRowFieldMappings.forEach(({ name }) => {
      const isValid = validateField(name, data[name] || '');
      if (!isValid) {
        formValid = false;
        fieldErrors[name] = 'This field is required';
      }
      if(Number(data["marks_obtained"]) > Number(data["marks_total"])){
        formValid = false;
        fieldErrors["marks_obtained"] = 'Obtained marks cannot be greater than total marks'
      }
    });
    setErrors(fieldErrors);
    setIsFormValid(formValid);
  }, [validateField]);

  const handleModalChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value, type, checked } = event.target;
      setModalData((prevData) => {
        const updatedData = { ...prevData, [name]: type === 'checkbox' ? checked : value };
        validateField(name, value);
        validateForm(updatedData);
        return updatedData;
      });
    },
    [validateField, validateForm],
  );

  useEffect(() => {
    if (isOpen) {
      const { marks_obtained, marks_total } = extractMarks(formData.written_feedback);
      setModalData({ ...formData, marks_obtained, marks_total });
    }
  }, [isOpen, formData, extractMarks]);

  useEffect(() => {
    setModalData((prevData) => ({
      ...prevData,
      written_feedback:
        prevData.marks_obtained && prevData.marks_total
          ? `${prevData.marks_obtained}/${prevData.marks_total}`
          : '',
    }));
  }, [modalData.marks_obtained, modalData.marks_total]);

  const handleSave = useCallback(() => {
    if (isFormValid) {
      editCandidateInline(modalData);
      onClose();
    }
  }, [modalData, editCandidateInline, onClose, isFormValid]);

  const handleCancel = useCallback(() => {
    setErrors({});
    onClose();
  }, [onClose]);

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth='lg' fullWidth>
      <DialogTitle
        sx={{ display: 'flex', justifyContent: 'center', borderBottom: '2px solid #193C6D', pb: 1 }}
        color='primary'
      >
        Edit Candidate Row
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={2} pt={2}>
          {EditRowFieldMappings.map(({ name, label, maxlength, type }) => (
            <Grid item xs={12} sm={6} md={4} key={name}>
              <TextField
                label={label}
                name={name}
                size='small'
                value={modalData[name] ?? ''}
                onChange={handleModalChange}
                variant='outlined'
                fullWidth
                type={type || 'text'}
                error={!!errors[name]}
                helperText={errors[name] || ''}
                inputProps={maxlength ? { maxLength: maxlength } : {}}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '25px',
                    '& fieldset': { borderColor: 'primary' },
                    '&:hover fieldset': { borderColor: 'primary' },
                    '&.Mui-focused fieldset': { borderColor: 'primary' },
                  },
                }}
              />
            </Grid>
          ))}
        </Grid>

        <Box>
          <ActionButton
            sx={{
              '&.Mui-disabled': {
                color: 'white',
                cursor: 'not-allowed',
                pointerEvents: 'auto',
                bgcolor: 'grey',
              },
            }}
            onClick={handleSave}
            disabled={!isFormValid}
          >
            Save
          </ActionButton>
          <ActionButton
            sx={{ mx: 1 }}
            onClick={handleCancel}
          >
            Cancel
          </ActionButton>
        </Box>
      </DialogContent>
    </Dialog>
  );
};


const mapStateToProps = (state: RootState) => ({
  isEditCandidateInline: recruitmentEntity.getRecruitment(state).editCandidateInline,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  editCandidateInline: (data: dataPayloadType) => dispatch(editCandidateInline.request({ data })),
})

export default connect(mapStateToProps, mapDispatchToProps)(EditRow)
