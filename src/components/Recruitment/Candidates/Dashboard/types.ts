export interface Candidate {
  id: string
  name: string
  email: string
  status: string
  dob: string
  phone_no: string
  stage: string
  qualification: string
  experience: string
  position: string
  date_time: string | null
  action?: any
}

type Option = string | number

export interface CandidateListType {
  count: number
  data: Candidate[]
  length: number
}

export interface ApiResponseItem {
  id: number
  round_name: string
}

export interface FetchCandidatesFilters {
  id_tag: Option[]
  from_date: string | null
  to_date: string | null
  status: Option[]
  missingInfo: boolean
  pageNumber: number
  limit: number
  interviewers: string[] | null
  recruiters: Option[]
  position: Option[]
  isChartCall: boolean
  search_input: string
}

export type ColumnType = {
  label: string
  key: keyof Candidate
}

export interface PostCandidateParams {
  [key: string]: string
}

export interface Attachment {
  resume: string
  email: string
  id: number
  signed_url: string
}

export interface VideoURL {
  candidateVideoUrl: string
  questions: []
}

export interface SendMailCandidateParams {
  emailList: string[]
  id_round: string
  templateId: string
  dateTime: string
  mute: string
}

export interface candidateProps {
  request: Candidate
  deleteCandidateById: (param: PostCandidateParams) => void
  fetchViewAttachmentsCandidate: (param: PostCandidateParams) => void
  viewAttachment: Attachment
  fetchVideoUrl: (param: PostCandidateParams) => void
  getVideoUrl: VideoURL
}

export interface candidateTableProp {
  candidateList: CandidateListType
  fetchCandidatesByFIltersData: (filters: FetchCandidatesFilters) => void
  isCandidatesByFiltersLoaded: Boolean
  isCandidateDeleted: Boolean
  isEditCandidateInline: any
  isCandidateMailSent: Boolean
  resetCandidatesByFIltersData: () => void
  editCandidateInlineReset: () => void
}
export interface candidateTableFilterProp {
  roundOptions: ApiResponseItem[][]
  fetchDateTimeByRoundData: (param: PostCandidateParams) => void
  scheduleOptions: ApiResponseItem[][]
  fetchTemplateByRoundData: (param: PostCandidateParams) => void
  templateOptions: ApiResponseItem[][]
  sendmailCandidateByIds: (param: SendMailCandidateParams) => void
  isCandidateMailSent: Boolean
}

export interface candidateFilterbarProp {
  roundOptions: ApiResponseItem[][]
  positionOptions: ApiResponseItem[][]
  tagOptions: ApiResponseItem[][]
  recruiterOptions: ApiResponseItem[][]
  fetchRoundsData: () => void
  fetchPositionsData: () => void
  fetchRecruitersData: ({}) => void
  fetchTagsData: () => void
}

export interface EditRowProps {
  isOpen: boolean
  onClose: () => void
  formData: Record<string, any>
  isEditCandidateInline: Boolean
  editCandidateInline: (param: PostCandidateParams) => void
}

export interface VideoQuestionPreviewProps {
  videoUrl: string
  questions: string[]
}
