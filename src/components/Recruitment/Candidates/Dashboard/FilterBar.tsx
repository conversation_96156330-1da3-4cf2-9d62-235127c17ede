import { useCallback, useEffect } from 'react'
import { Box, Grid } from '@mui/material'
import RestartAltIcon from '@mui/icons-material/RestartAlt'
import { ActionButton } from '../../../HolidayList/HolidaysStyles'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity } from '../../../../reducers'
import { fetchPositions, fetchRecruiters, fetchRounds, fetchTags } from '../../../../actions'
import { connect } from 'react-redux'
import { Dispatch } from 'redux'
import CustomDateRangePicker from '../../Common/GenericDateRangePicker'
import { MultiSelectDropdown } from '../../Common'
import { formatForMultiSelect } from './utils'
import { toast } from 'react-toastify'
import { useCandidateContext } from '../CandidateContext'
import { candidateFilterbarProp } from './types'

const sizes = { lg: 2.2, md: 4, sm: 6, xs: 12 }

const FilterBar = ({
  roundOptions,
  positionOptions,
  tagOptions,
  recruiterOptions,
  fetchRoundsData,
  fetchPositionsData,
  fetchRecruitersData,
  fetchTagsData,
}: candidateFilterbarProp) => {
  const { state, dispatch } = useCandidateContext()

  useEffect(() => {
    const fetchAllData = async () => {
      try {
        await Promise.all([
          fetchRoundsData(),
          fetchPositionsData(),
          fetchRecruitersData({}),
          fetchTagsData(),
        ])
      } catch (error) {
        toast.error(`Unable to load filter's dropdown data`)
      }
    }
    fetchAllData()
  }, [fetchRoundsData, fetchPositionsData, fetchRecruitersData, fetchTagsData])

  const handleFilterChange = useCallback(
    (filterName: keyof typeof state.filters, selectedValues: (string | number)[]) => {
      dispatch({
        type: 'SET_FILTERS',
        payload: { ...state.filters, [filterName]: selectedValues },
      })
    },
    [dispatch, state.filters],
  )

  const handleReset = useCallback(() => {
    dispatch({
      type: 'SET_FILTERS',
      payload: { positions: [], rounds: [], tags: [], recruiters: [] },
    })
    dispatch({ type: 'SET_FROM_DATE', payload: null })
    dispatch({ type: 'SET_TO_DATE', payload: null })
    dispatch({ type: 'SET_ROUND_FILTER', payload: '' })
    dispatch({ type: 'SET_TEMPLATE_FILTER', payload: '' })
    dispatch({ type: 'SET_SCHEDULE_FILTER', payload: '' })
  }, [])

  return (
    <Box
      sx={{
        my: '1rem',
      }}
    >
      <Grid container spacing={2} alignItems='center' justifyContent='space-evenly'>
        <Grid item {...sizes}>
          <MultiSelectDropdown
            label='Positions'
            options={formatForMultiSelect(positionOptions, 'name', 'id', 1)}
            selectedOptions={state.filters.positions}
            setSelectedOptions={(values) => handleFilterChange('positions', values)}
          />
        </Grid>
        <Grid item {...sizes}>
          <MultiSelectDropdown
            label='Select Rounds'
            options={formatForMultiSelect(roundOptions, 'round_name', 'id', 0)}
            selectedOptions={state.filters.rounds}
            setSelectedOptions={(values) => handleFilterChange('rounds', values)}
          />
        </Grid>
        <Grid item {...sizes}>
          <MultiSelectDropdown
            label='Select Tags'
            options={formatForMultiSelect(tagOptions, 'name', 'id', 0)}
            selectedOptions={state.filters.tags}
            setSelectedOptions={(values) => handleFilterChange('tags', values)}
          />
        </Grid>
        <Grid item {...sizes}>
          <CustomDateRangePicker
            fromDate={state.fromDate}
            setFromDate={(value) => dispatch({ type: 'SET_FROM_DATE', payload: value })}
            toDate={state.toDate}
            setToDate={(value) => dispatch({ type: 'SET_TO_DATE', payload: value })}
            onApply={() => dispatch({ type: 'SET_APPLY_TRIGGER', payload: state.applyTrigger + 1 })}
          />
        </Grid>
        <Grid item {...sizes}>
          <MultiSelectDropdown
            label='Select Recruiters'
            options={formatForMultiSelect(recruiterOptions, 'name', 'id', 0)}
            selectedOptions={state.filters.recruiters}
            setSelectedOptions={(values) => handleFilterChange('recruiters', values)}
          />
        </Grid>
        <Grid item lg={1} md={4} sm={6} xs={12}>
          <ActionButton
            variant='outlined'
            onClick={handleReset}
            startIcon={<RestartAltIcon />}
            fullWidth
          >
            Reset
          </ActionButton>
        </Grid>
      </Grid>
    </Box>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    roundOptions: recruitmentEntity.getRecruitment(state).getRoundData,
    positionOptions: recruitmentEntity.getRecruitment(state).getPositionData,
    tagOptions: recruitmentEntity.getRecruitment(state).getTagData,
    recruiterOptions: recruitmentEntity.getRecruitment(state).getRecruiterData,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchRoundsData: () => dispatch(fetchRounds.request()),
    fetchPositionsData: () => dispatch(fetchPositions.request()),
    fetchTagsData: () => dispatch(fetchTags.request()),
    fetchRecruitersData: (data: {}) => dispatch(fetchRecruiters.request({ data })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(FilterBar)
