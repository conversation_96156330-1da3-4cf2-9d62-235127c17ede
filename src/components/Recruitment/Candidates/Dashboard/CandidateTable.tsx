import React, { useEffect, useState } from 'react'
import {
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableBody,
  Paper,
  TableSortLabel,
  Box,
  Pagination,
  Checkbox,
  Typography,
  Menu,
  MenuItem,
} from '@mui/material'
import CandidateTableAction from './CandidateTableAction'
import { columns, handleRequestSort, StyledCheckbox, StyledTableCell } from './utils'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { Dispatch } from 'redux'
import { editCandidateInline, FetchCandidateByFilters } from '../../../../actions'
import { connect } from 'react-redux'
import Loader from '../../../Common/Loader'
import { useCandidateContext } from '../CandidateContext'
import { Candidate, candidateTableProp } from './types'
import { toast } from 'react-toastify'

function CandidateTable({
  candidateList,
  fetchCandidatesByFIltersData,
  isCandidatesByFiltersLoaded,
  isCandidateDeleted,
  isEditCandidateInline,
  isCandidateMailSent,
  resetCandidatesByFIltersData,
  editCandidateInlineReset,
}: candidateTableProp) {
  const { state, dispatch } = useCandidateContext()
  const [reload, setReload] = useState<Boolean>(false)
  const rowsPerPage = 10
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number
    mouseY: number
    candidate: Candidate | null
  } | null>(null)
  const handleContextMenu = (event: React.MouseEvent, candidate: Candidate) => {
    event.preventDefault() // prevent default browser right-click menu
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      candidate,
    })
  }

  const handleCloseContextMenu = () => {
    setContextMenu(null)
  }

  useEffect(() => {
    fetchCandidatesByFIltersData({
      id_tag: state.filters.tags,
      from_date: state.fromDate ? state.fromDate.format('DD-MM-YYYY') : null,
      to_date: state.toDate ? state.toDate.format('DD-MM-YYYY') : null,
      status: state.filters.rounds,
      missingInfo: false,
      pageNumber: state.page,
      limit: rowsPerPage,
      interviewers: null,
      recruiters: state.filters.recruiters,
      position: state.filters.positions,
      isChartCall: false,
      search_input: state.searchQuery,
    })
  }, [
    state.page,
    state.filters.tags,
    state.filters.positions,
    state.applyTrigger,
    state.filters.rounds,
    state.filters.recruiters,
    state.searchQuery,
    isCandidateDeleted,
    reload,
    isCandidateMailSent,
  ])

  useEffect(() => {
    if (isEditCandidateInline && Object.keys(isEditCandidateInline).length > 0) {
      if (
        isEditCandidateInline.message === 'Candidate updated successfully.' &&
        isEditCandidateInline.data
      ) {
        toast.success(isEditCandidateInline.message)
        editCandidateInlineReset()
        setReload((prev) => !prev)
      } else if (isEditCandidateInline.code === -123 && isEditCandidateInline.message) {
        toast.error(isEditCandidateInline.message)
      }
    }
    return () => {
      resetCandidatesByFIltersData()
    }
  }, [isEditCandidateInline])

  const dataCount = candidateList?.count || 0
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')
  const [orderBy, setOrderBy] = useState<string>('')

  const handlePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
    dispatch({ type: 'SET_PAGE', payload: newPage })
  }

  const handleSelectRow = (cId: string) => {
    const newSelectedRows = state.selectedRows.includes(cId)
      ? state.selectedRows.filter((id) => id !== cId)
      : [...state.selectedRows, cId]

    dispatch({ type: 'SET_SELECTED_ROWS', payload: newSelectedRows })
  }

  const sortedList = candidateList?.data?.sort((a: any, b: any) => {
    if (!orderBy) return 0
    const isAsc = order === 'asc'
    return isAsc
      ? a[orderBy].toString().localeCompare(b[orderBy].toString())
      : b[orderBy].toString().localeCompare(a[orderBy].toString())
  })

  return (
    <>
      <Loader state={!isCandidatesByFiltersLoaded} />
      <TableContainer component={Paper} sx={{ cursor: 'pointer' }}>
        {sortedList?.length > 0 ? (
          <Table sx={{ minWidth: 700 }}>
            <TableHead>
              <TableRow>
                <StyledTableCell>
                  <StyledCheckbox
                    indeterminate={
                      state.selectedRows.length > 0 && state.selectedRows.length < sortedList.length
                    }
                    checked={state.selectedRows.length === sortedList.length}
                    onChange={(e) =>
                      dispatch({
                        type: 'SET_SELECTED_ROWS',
                        payload: e.target.checked
                          ? sortedList.map((r: { id: string }) => r.id)
                          : [],
                      })
                    }
                  />
                </StyledTableCell>
                {columns.map(({ label, key }) =>
                  label === 'Action' ||
                  label === 'Position' ||
                  label === 'Status' ||
                  label === 'Schedule' ? (
                    <StyledTableCell key={key}>{label}</StyledTableCell>
                  ) : (
                    <StyledTableCell key={key}>
                      <TableSortLabel
                        active={orderBy === key}
                        direction={orderBy === key ? order : 'asc'}
                        onClick={() =>
                          handleRequestSort({ property: key, order, orderBy, setOrder, setOrderBy })
                        }
                      >
                        {label}
                      </TableSortLabel>
                    </StyledTableCell>
                  ),
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedList.map((request: Candidate) => (
                <TableRow
                  key={request.id}
                  hover
                  onContextMenu={(e) => handleContextMenu(e, request)}
                >
                  <StyledTableCell>
                    <Checkbox
                      checked={state.selectedRows.includes(request.id)}
                      onChange={() => handleSelectRow(request.id)}
                    />
                  </StyledTableCell>
                  {columns.map(({ key }) => (
                    <StyledTableCell key={key}>
                      {key === 'action' ? (
                        <>
                          <CandidateTableAction request={request} />
                        </>
                      ) : request[key] != null ? (
                        request[key]
                      ) : (
                        'NA'
                      )}
                    </StyledTableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Typography variant='h5' textAlign='center'>
            No Data Found
          </Typography>
        )}
      </TableContainer>
      {sortedList?.length > 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
          <Pagination
            count={Math.ceil(dataCount / rowsPerPage)}
            color='primary'
            page={state.page}
            onChange={handlePageChange}
          />
        </Box>
      )}

      <Menu
        open={!!contextMenu}
        onClose={handleCloseContextMenu}
        anchorReference='anchorPosition'
        anchorPosition={
          contextMenu !== null ? { top: contextMenu.mouseY, left: contextMenu.mouseX } : undefined
        }
      >
        <MenuItem disabled>{contextMenu?.candidate?.name || 'Candidate'}</MenuItem>
        <MenuItem
          onClick={() => {
            // Example: View Details
            toast.info(`Viewing ${contextMenu?.candidate?.name}`)
            handleCloseContextMenu()
          }}
        >
          View Details
        </MenuItem>
        <MenuItem
          onClick={() => {
            // Example: Email Candidate
            toast.info(`Sending email to ${contextMenu?.candidate?.email}`)
            handleCloseContextMenu()
          }}
        >
          Email Candidate
        </MenuItem>
      </Menu>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    candidateList: recruitmentEntity.getRecruitment(state).getCandidatesByFilters,
    isCandidatesByFiltersLoaded:
      recruitmentStateUI.getRecruitment(state).isCandidatesByFiltersLoaded,
    isCandidateDeleted: recruitmentStateUI.getRecruitment(state).isCandidateDeleted,
    isEditCandidateInline: recruitmentEntity.getRecruitment(state).editCandidateInline,
    isCandidateMailSent: recruitmentStateUI.getRecruitment(state).isCandidateMailSent,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchCandidatesByFIltersData: (data: {}) => dispatch(FetchCandidateByFilters.request({ data })),
    resetCandidatesByFIltersData: () => dispatch(FetchCandidateByFilters.reset()),
    editCandidateInlineReset: () => dispatch(editCandidateInline.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CandidateTable)
