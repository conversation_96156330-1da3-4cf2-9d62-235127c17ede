import React from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Box,
  SelectChangeEvent,
  TextField,
} from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import Chip from "@mui/material/Chip";

interface StatusTagsProps {
  formData: {
    status: string;
    stage: string;
  };
  errors: {
    status?: string;
    stage?: string;
  };
  onChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>
  ) => void;
  statusOptions: string[];
  stageOptions: Array<{ id: string, name: string }>;
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  tagOptions: string[];
}

const StatusTags: React.FC<StatusTagsProps> = ({
  formData,
  errors,
  onChange,
  statusOptions,
  stageOptions,
  tags,
  onTagsChange,
  tagOptions,
}) => {
  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: { xs: "1fr", sm: "repeat(3, 1fr)" },
        gap: 2,
        width: "100%",
        alignItems: "center",
      }}
    >
      <FormControl
        size="small"
        sx={{
          width: "100% ",
          height: "56px",
          "& .MuiInputBase-root": {
            height: "56px",
            paddingLeft: "8px",
            paddingRight: "8px",
          },
          "& .MuiOutlinedInput-root": { borderRadius: "50px" },
          "& .MuiInputLabel-root": {
            top: "14px",
            left: "16px",
            transform: "translateY(0)",
          },
          "& .MuiInputLabel-shrink": {
            top: "-5px",
            left: "16px",
            transform: "scale(0.75)",
          },
        }}
        error={!!errors.status}
      >
        <InputLabel>Status</InputLabel>
        <Select name="status" value={formData.status || ""} onChange={onChange} label="Status">
          {statusOptions.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>
        {errors.status && <FormHelperText>{errors.status}</FormHelperText>}
      </FormControl>

      <FormControl
        size="small"
        sx={{
          width: "100% ",
          height: "56px",
          "& .MuiInputBase-root": {
            height: "56px",
            paddingLeft: "8px",
            paddingRight: "8px",
          },
          "& .MuiOutlinedInput-root": { borderRadius: "50px" },
          "& .MuiInputLabel-root": {
            top: "14px",
            left: "16px",
            transform: "translateY(0)",
          },
          "& .MuiInputLabel-shrink": {
            top: "-5px",
            left: "16px",
            transform: "scale(0.75)",
          },
        }}
        error={!!errors.stage}
      >
        <InputLabel>Stage</InputLabel>
        <Select name="stage" value={formData.stage || ""} onChange={onChange} label="Stage">
          {stageOptions.map((option) => (
            <MenuItem key={option.id} value={option.name}>
              {option.name}
            </MenuItem>
          ))}
        </Select>
        {errors.stage && <FormHelperText>{errors.stage}</FormHelperText>}
      </FormControl>

      <Box sx={{ gridColumn: "span 1", display: "flex", alignItems: "center" }}>
        <Autocomplete
          fullWidth
          multiple
          freeSolo
          options={tagOptions}
          value={tags}
          onChange={(_, newValue) => onTagsChange(newValue)}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip variant="outlined" label={option} {...getTagProps({ index })} />
            ))
          }
          renderInput={(params) => (
            <TextField {...params} label="Enter a New Tag" variant="standard" required={false} size="small" />
          )}
        />
      </Box>
    </Box>
  );
};

export default StatusTags;