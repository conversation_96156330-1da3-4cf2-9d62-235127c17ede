import React, { useState } from 'react'
import {
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Box,
  Typography,
  IconButton,
} from '@mui/material'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { styled } from '@mui/material'
import { Download, Close } from '@mui/icons-material'
import { useParams } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import {
  addFeedbackCandidate,
  addUploadAssignment,
  viewCandidateFeedback,
} from '../../../../actions'

interface NoteData {
  feedback: string
  note_data: string
}

interface NoteEditorProps {
  onChange?: (value: boolean) => void
  setNoteIsVisible: (value: boolean) => void
  setNotedata: (value: NoteData) => void
  refresh: boolean
  setRefresh: (value: boolean) => void
}

const NoteEditor: React.FC<NoteEditorProps> = ({
  onChange,
  setNoteIsVisible,
  setNotedata,
  refresh,
  setRefresh,
}) => {
  const [noteType, setNoteType] = useState('screening')
  const [editorData, setEditorData] = useState('')
  const [files, setFiles] = useState<File[]>([])
  const { id } = useParams()
  const dispatch = useDispatch()
  const isAddDisabled = !editorData.replace(/<[^>]*>/g, '').trim()

  const handleEditorChange = (event: any, editor: any) => {
    const data = editor.getData()
    setEditorData(data)
  }

  const handleClose = () => {
    onChange?.(false)
  }

  const handleAddNote = () => {
    const payload = {
      feedback: editorData,
      candidate_id: id || '',
      grade: '',
      feedback_type: 3,
      assignment: '',
      note_type: noteType,
    }
    setNoteIsVisible(true)
    setNoteIsVisible(false)
    const obj = {
      feedback: payload.feedback,
      note_data: payload.note_type,
    }
    setNotedata(obj)
    setRefresh(!refresh)
    dispatch(addFeedbackCandidate.request(payload))

    files.forEach((file) => {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('candidate_id', id || '')
      dispatch(addUploadAssignment.request({ data: formData }))
    })

    handleClose()
  }

  const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
  })

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFiles([...files, ...Array.from(event.target.files)])
    }
  }

  const handleRemoveFile = (fileName: string) => {
    setFiles(files.filter((file) => file.name !== fileName))
  }

  const buttonStyles = {
    height: '45px',
    borderRadius: '50px',
    fontWeight: 'bold',
    textTransform: 'uppercase',
    padding: '10px 25px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  }

  return (
    <div>
      <Box>
        <Typography
          variant='h6'
          sx={{
            color: '#193C6D',
            fontWeight: 'bold',
            margin: 'auto',
            fontSize: '28px',
            borderRadius: '30px',
            textAlign: 'center',
          }}
        >
          Add Notes
        </Typography>
      </Box>
      <Box
        sx={{
          marginBottom: 1,
          marginTop: '15px',
          height: '90px',
          overflowY: 'auto',
          border: '1px solid #ccc',
          borderRadius: 1,
          padding: 1,
        }}
      >
        <CKEditor editor={ClassicEditor} data={editorData} onChange={handleEditorChange} />
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', marginTop: 2 }}>
        <FormLabel
          component='legend'
          sx={{
            marginRight: 2,
            fontWeight: 'bold',
            margin: '10px',
            color: '#193C6D',
            fontSize: '25px',
            width: '200px',
            fontFamily: 'Montserrat-SemiBold',
          }}
        >
          Note type:
        </FormLabel>
        <FormControl component='fieldset'>
          <RadioGroup row value={noteType} onChange={(e) => setNoteType(e.target.value)}>
            <FormControlLabel
              value='follow-up'
              control={<Radio />}
              label='Follow-Up'
              sx={{
                fontFamily: 'Montserrat-Medium',
                '& .MuiFormControlLabel-label': { fontSize: '20px' },
              }}
            />
            <FormControlLabel
              value='screening'
              control={<Radio />}
              label='Screening'
              sx={{
                fontFamily: 'Montserrat-Medium',
                '& .MuiFormControlLabel-label': { fontSize: '20px' },
              }}
            />
          </RadioGroup>
        </FormControl>
      </Box>
      <br />
      <br />
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            component='label'
            variant='contained'
            sx={{
              ...buttonStyles,
              width: '300px',
              backgroundColor: '#193C6D',
              color: '#FFFFFF',
            }}
            startIcon={<CloudUploadIcon />}
          >
            Upload Assignment
            <VisuallyHiddenInput type='file' multiple onChange={handleFileUpload} />
          </Button>
        </Box>

        {files.length > 0 && (
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              maxWidth: '50%',
              fontFamily: 'Montserrat-Medium',
            }}
          >
            {files.map((file) => (
              <Box
                key={file.name}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                }}
              >
                <Typography variant='body2'>{file.name}</Typography>
                <IconButton
                  sx={{ color: '#193C6D' }}
                  href={URL.createObjectURL(file)}
                  download={file.name}
                >
                  <Download />
                </IconButton>

                <IconButton
                  sx={{
                    color: 'black',
                    padding: '2px',
                  }}
                  onClick={() => handleRemoveFile(file.name)}
                >
                  <Close />
                </IconButton>
              </Box>
            ))}
          </Box>
        )}
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: 2, gap: 2 }}>
        <Button
          variant='contained'
          sx={{
            ' &:hover': {
              backgroundColor: '#E0E0E0',
              color: 'black',
              boxShadow: '0 0 0 0.2',
            },
            backgroundColor: '#E0E0E0',
            fontSize: '20px',
            color: 'black',
            borderRadius: '20px',
            padding: '6px 20px',
            width: '120px',
            marginBottom: '20px',
            fontFamily: 'Montserrat-SemiBold',
          }}
          onClick={handleClose}
        >
          Cancel
        </Button>
        <Button
          variant='contained'
          sx={{
            backgroundColor: '#1A3A6F',
            color: 'white',
            borderRadius: '20px',
            padding: '6px 20px',
            width: '120px',
            fontSize: '20px',
            marginBottom: '20px',
            marginRight: '24px',
            fontFamily: 'Montserrat-SemiBold',
          }}
          onClick={handleAddNote}
          disabled={isAddDisabled}
        >
          Add
        </Button>
      </Box>
    </div>
  )
}

export default NoteEditor
