import React, { useEffect, useState } from 'react'
import { Box, Typography, Button, Dialog, IconButton, Avatar } from '@mui/material'
import InterviewFeedback from './InterviewFeedback'
import NoteEditor from './NoteEditor'
import CandidateForm from './CandidateForm'
import DeleteConfirmation from './DeleteConfirmation'
import { RootState } from '../../../../configureStore'
import { recruitmentEntity, recruitmentStateUI } from '../../../../reducers'
import { Dispatch } from 'redux'
import {
  addFeedbackCandidate,
  addUploadAssignment,
  addUploadResume,
  deleteAttachment,
  deleteCandidateFeedback,
  editCandidate,
  fetchAddQualification,
  fetchCandidatePosition,
  fetchFeedback,
  fetchPositions,
  fetchTags,
  getCandidatebyID,
  viewCandidateFeedback,
} from '../../../../actions'
import { connect } from 'react-redux'
import DocumentsTable from './DocumentsTable'
import FeedbackPopUp from './FeedbackPopUp '
import { useParams } from 'react-router-dom'
import { isObject } from 'formik'
import MarksFeedbackShow from './MarksFeedbackShow'
import NoteShow from './NoteShow'
import { toast } from 'react-toastify'
import { handleToasts } from 'components/Recruitment/Common/notifications'

interface Attachment {
  resume: string
  email: string
  id: number
}
interface NoteData {
  feedback: string
  note_data: string
}

interface CandidateData {
  id: number
  name: string
  email: string
  phone_no: string
  experience_id: number
  position_id: number
  round_id: string
  qualification_id: string
  subjects: string
  dob: string
  attachments: Attachment[]
}
interface CandidateFeedback {
  id: number
  feedback: string
  candidate_id: number
  grade: string
  created_at: string
  assignment: string
  recruiter_name: string
  recruiter_image: string
  feedback_type: number
  note_type: string
}

interface Document {
  id: number
  name: string
}

interface Experience {
  id: number
  experience: string
}

interface JobDescription {
  id: number
  name: string
}

function CandidateProfile(props: any) {
  const [openDialog, setOpenDialog] = useState(false)
  const [openEdit, setOpenEdit] = useState(false)
  const [openFeedback, setOpenFeedback] = useState(false)
  const [openNoteEditor, setOpenNoteEditor] = useState(false)
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false)
  const [marksFeedback, setMarksFeeback] = useState('')
  const [notedata, setNotedata] = useState<NoteData>()
  const [isVisible, setIsVisible] = useState(false)
  const [isNoteVisible, setNoteIsVisible] = useState(false)
  const [candidateId, setCandidateId] = useState(-1)
  const [refresh, setRefresh] = useState(false)

  const [candidateName, setCandidateName] = useState('')
  const [candidateEmail, setCandidateEmail] = useState('')
  const [candidatePhone, setCandidatePhone] = useState('')
  const [experience, setExperience] = useState('')
  const [jobRole, setJobRole] = useState('')
  const [qualificationId, setQualificationId] = useState('')
  const [roundId, setRoundId] = useState('')
  const [subjects, setSubjects] = useState('')
  const [dob, setDOB] = useState('')
  const [documents, setDocuments] = useState<Document[]>([])
  const [statusData, setStatusData] = useState([])

  const { id } = useParams()
  const {
    fetchpositionsData,
    positionExperienceOptions,
    fetchCandidatebyID,
    CandidateByIDOptions,
    fetchAddQualification,
    AddQualificationOptions,
    fetchTag,
    TagOptions,
    fetchViewData,
    UploadResume,
    addFeedbackCandidate,
    addUploadAssignment,
    deleteAttachment,
    editCandidate,
    viewCandidateFeedback,
    CandidateFeedback,
    deleteCandidateFeedback,
    addUploadResume,
    isCandidateEdited,
    resetCandidateEdited,
    isFeedbackAdded,
    resetAddFeedback,
    isAttachmentDeleted,
    resetDeleteAttachment,
    isFeedbackDeleted,
    resetDeleteFeedback,
  } = props

  useEffect(() => {
    handleToasts([
      {
        condition: isCandidateEdited,
        message: 'Candidate Details Successfully Updated',
        reset: resetCandidateEdited,
      },
      {
        condition: isFeedbackAdded,
        message: 'Candidate Feedback Successfully Added',
        reset: resetAddFeedback,
      },
      {
        condition: isAttachmentDeleted,
        message: 'Candidate Attachment Successfully Deleted',
        reset: resetDeleteAttachment,
      },
    ])
  }, [
    isCandidateEdited,
    isFeedbackAdded,
    isAttachmentDeleted,
    resetCandidateEdited,
    resetAddFeedback,
    resetDeleteAttachment,
  ])

  useEffect(() => {
    fetchpositionsData()
    if (id) {
      fetchCandidatebyID(id)
      viewCandidateFeedback({ candidate_id: id, flag: 'Admin' })
    }
    fetchAddQualification()
    fetchTag()
  }, [
    id,
    fetchCandidatebyID,
    fetchpositionsData,
    fetchAddQualification,
    fetchTag,
    viewCandidateFeedback,
  ])

  let documentdata
  let attachmentData
  if (isObject(CandidateByIDOptions[0])) {
    documentdata = CandidateByIDOptions[0]['0']
    attachmentData = CandidateByIDOptions[0]['attachments']
  }
  const Data =
    Array.isArray(CandidateFeedback) && Array.isArray(CandidateFeedback[0])
      ? CandidateFeedback[0]
      : []

  useEffect(() => {
    if (CandidateByIDOptions && CandidateByIDOptions.length > 0) {
      const candidateData = CandidateByIDOptions[0][0] as CandidateData
      setCandidateName(candidateData.name)
      setCandidateId(candidateData.id)
      setCandidateEmail(candidateData.email)
      setCandidatePhone(candidateData.phone_no)
      setSubjects(candidateData.subjects)
      setQualificationId(candidateData.qualification_id)
      setRoundId(candidateData.round_id)
      setDOB(candidateData.dob)

      const experienceData = positionExperienceOptions[0]?.find(
        (exp: Experience) => exp.id === candidateData.experience_id,
      )
      const jobDescriptionData = positionExperienceOptions[1]?.find(
        (job: JobDescription) => job.id === candidateData.position_id,
      )

      if (experienceData) {
        setExperience(experienceData.experience)
      }

      if (jobDescriptionData) {
        setJobRole(jobDescriptionData.name)
      }

      const attachments = candidateData.attachments
      if (attachments && attachments.length > 0) {
        setDocuments(attachments.map((att) => ({ id: att.id, name: att.resume })))
      }
    }
  }, [CandidateByIDOptions, positionExperienceOptions])

  const handleDelete = (id: number) => {
    deleteCandidateFeedback({ id: id })
    toast.success('Candidate Feedback Successfully Deleted', {
      position: 'top-right',
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    })
    setRefresh((prev) => !prev)
  }

  const handleAttachmentDelete = (data: any) => {
    deleteAttachment(data)
    // Clear the documents state when attachment is deleted
    setDocuments([])
    // Refresh the candidate data to get updated state
    setTimeout(() => fetchCandidatebyID(id), 100)
  }

  const handleResumeUpload = (file: File) => {
    // Create form data for the file upload
    const formData = new FormData()
    formData.append('file', file)
    formData.append('originalName', file.name)
    formData.append('candidate_id', id || '')

    // Upload the resume with the original filename
    addUploadResume(formData)

    // Update local state immediately for better UX
    setDocuments([{ id: Date.now(), name: file.name }])

    // Refresh candidate data to get updated state from API
    setTimeout(() => {
      fetchCandidatebyID(id)
      viewCandidateFeedback({ candidate_id: id, flag: 'Admin' })
    }, 500)
  }

  const handleSave = (data: any) => {
    setOpenEdit(false)
    if (id) {
      setRefresh((prev) => !prev)
    }
  }

  useEffect(() => {
    setTimeout(() => fetchCandidatebyID(id), 100)
    setTimeout(() => viewCandidateFeedback({ candidate_id: id, flag: 'Admin' }), 100)
  }, [refresh])

  const experienceOptions =
    positionExperienceOptions[0]?.map((exp: any) => ({
      id: exp.id,
      experience: exp.experience,
    })) || []
  const positionOptions =
    positionExperienceOptions[1]?.map((pos: any) => ({
      id: pos.id,
      name: pos.name,
    })) || []

  return (
    <div
      style={{
        padding: '20px 15px',
        backgroundColor: 'rgb(231,235,240)',
        fontFamily: 'Montserrat-SemiBold',
      }}
    >
      <Box sx={{ minHeight: '80vh', bgcolor: 'white', padding: 2 }}>
        <Box
          sx={{
            color: '#193C6D',
            padding: 3,
            borderRadius: 1,
            border: '1px solid #c8cee3',
          }}
        >
          <Typography
            variant='h2'
            fontWeight='bold'
            sx={{ fontSize: '28px', fontFamily: 'Montserrat-Medium' }}
          >
            {candidateName}
          </Typography>
          <Typography
            variant='body1'
            sx={{
              fontSize: '18px',
              color: '#193C6D',
              marginTop: '10px',
              fontFamily: 'Montserrat-Medium',
            }}
          >
            ({candidateEmail}, {candidatePhone}, {experience}, {jobRole})
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            height: '45px',
            alignItems: 'space-evenly',
            marginTop: 4,
            width: '100%',
            gap: 5,
            backgroundColor: '',
          }}
        >
          <Button
            fullWidth
            variant='outlined'
            sx={{
              borderRadius: '35px',
              fontSize: '16px',
              width: '80%',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => setOpenFeedback(true)}
          >
            Interview Feedback
          </Button>
          <Button
            fullWidth
            variant='outlined'
            sx={{
              width: '80%',
              borderRadius: '35px',
              fontSize: '16px',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => setOpenDialog(true)}
          >
            Written
          </Button>
          <Button
            fullWidth
            variant='outlined'
            sx={{
              borderRadius: '35px',
              fontSize: '16px',
              width: '80%',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => setOpenNoteEditor(true)}
          >
            Notes
          </Button>
          <Button
            fullWidth
            variant='outlined'
            sx={{
              borderRadius: '35px',
              fontSize: '16px',
              padding: '0px',
              width: '80%',
              color: '#193C6D',
              border: '1px solid #c8cee3',
              backgroundColor: 'white',
              fontFamily: 'Montserrat-Medium',
            }}
            onClick={() => setOpenEdit(true)}
          >
            Edit
          </Button>
        </Box>

        <DocumentsTable
          documents={documents}
          onDelete={handleDelete}
          documentdata={documentdata}
          attachmentData={attachmentData}
          deleteAttachment={handleAttachmentDelete}
        />

        <FeedbackPopUp
          open={openDialog}
          onClose={() => setOpenDialog(false)}
          candidateId={id || ''}
          refresh={refresh}
          setRefresh={setRefresh}
          setMarksFeeback={setMarksFeeback}
          setIsVisible={setIsVisible}
        />
        <MarksFeedbackShow Data={Data} handleDelete={handleDelete} />

        <DeleteConfirmation open={openDeleteConfirm} onClose={() => setOpenDeleteConfirm(false)} />
        <InterviewFeedback
          refresh={refresh}
          setRefresh={setRefresh}
          open={openFeedback}
          handleClose={() => setOpenFeedback(false)}
          candidateId={id || ''}
        />
        <Dialog open={openEdit} onClose={() => setOpenEdit(false)} maxWidth='lg' fullWidth>
          <Box p={2}>
            <CandidateForm
              open={openEdit}
              handleClose={() => setOpenEdit(false)}
              handleSave={handleSave}
              positionOptions={positionOptions}
              experiencesOptions={experienceOptions}
              qualificationOptions={AddQualificationOptions?.[0] || []}
              tagsOptions={TagOptions}
              initialData={{
                id: candidateId,
                name: candidateName,
                email: candidateEmail,
                phone: candidatePhone,
                experience: experience,
                jobRole: jobRole,
                qualification: qualificationId,
                round: roundId,
                subject: subjects,
                dob: dob,
                blockemail: documentdata?.block_mail === '1' ? 'Yes' : 'No',
                status: documentdata?.status || '',
              }}
              editCandidate={editCandidate}
              uploadResume={handleResumeUpload}
            />
          </Box>
        </Dialog>

        <Dialog open={openNoteEditor} onClose={() => setOpenNoteEditor(false)} maxWidth='lg'>
          <Box p={2} sx={{ width: '750px' }}>
            <NoteEditor
              onChange={setOpenNoteEditor}
              setNoteIsVisible={setNoteIsVisible}
              setNotedata={setNotedata}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          </Box>
        </Dialog>
      </Box>
    </div>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    positionExperienceOptions: recruitmentEntity.getRecruitment(state).getPositionData || [[], []],
    CandidateByIDOptions: recruitmentEntity.getRecruitment(state).getCandidateByID,
    AddQualificationOptions: recruitmentEntity.getRecruitment(state).getAddQualificationData,
    TagOptions: recruitmentEntity.getRecruitment(state).getTagData,
    CandidateFeedback: recruitmentEntity.getRecruitment(state).viewCandidateFeedback,
    UploadResume: recruitmentEntity.getRecruitment(state).addUploadResume,
    UploadAssignment: recruitmentEntity.getRecruitment(state).addUploadAssignment,
    deleteCandidateFeedbackResponse:
      recruitmentEntity.getRecruitment(state).deleteCandidateFeedback,
    isCandidateEdited: recruitmentStateUI.getRecruitment(state).isCandidateEdited,
    isFeedbackAdded: recruitmentStateUI.getRecruitment(state).isFeedbackAdded,
    isAttachmentDeleted: recruitmentStateUI.getRecruitment(state).isAttachmentDeleted,
    isFeedbackDeleted: recruitmentStateUI.getRecruitment(state).isFeedbackDeleted,
  }
}

const mapDispatchToProps = (dispatch: Dispatch) => {
  return {
    fetchpositionsData: () => dispatch(fetchPositions.request()),
    fetchCandidatebyID: (id: string) => dispatch(getCandidatebyID.request({ id })),
    fetchAddQualification: () => dispatch(fetchAddQualification.request()),
    fetchTag: () => dispatch(fetchTags.request()),
    fetchViewData: () => dispatch(fetchCandidatePosition.request()),
    addFeedbackCandidate: (data: any) => () => dispatch(addFeedbackCandidate.request(data)),
    deleteAttachment: (data: any) => dispatch(deleteAttachment.request(data)),
    editCandidate: (data: any) => dispatch(editCandidate.request(data)),
    viewCandidateFeedback: (data: any) => dispatch(viewCandidateFeedback.request(data)),
    deleteCandidateFeedback: (data: any) => dispatch(deleteCandidateFeedback.request(data)),
    addUploadResume: (data: any) => dispatch(addUploadResume.request(data)),
    addUploadAssignment: (data: any) => dispatch(addUploadAssignment.request(data)),
    resetCandidateEdited: () => dispatch(editCandidate.reset()),
    resetAddFeedback: () => dispatch(addFeedbackCandidate.reset()),
    resetDeleteAttachment: () => dispatch(deleteAttachment.reset()),
    resetDeleteFeedback: () => dispatch(deleteCandidateFeedback.reset()),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(CandidateProfile)
