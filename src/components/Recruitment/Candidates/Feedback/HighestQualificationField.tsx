import React from 'react'
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
} from '@mui/material'

interface HighestQualificationFieldProps {
  value: string
  error?: string
  onChange: (e: SelectChangeEvent) => void
  options: { id: number; name: string }[]
}

const HighestQualificationField: React.FC<HighestQualificationFieldProps> = ({
  value,
  error,
  onChange,
  options,
}) => {
  return (
    <FormControl
      size='small'
      fullWidth
      error={!!error}
      sx={{
        margin: '0px 0px 8px 0px',

        width: { xs: '100%', sm: '48%' },
        '& .MuiOutlinedInput-root': { borderRadius: '50px', height: '50px' },
      }}
    >
      <InputLabel>Highest Qualification</InputLabel>
      <Select
        name='highestQualification'
        value={value}
        label='Highest Qualification'
        onChange={onChange}
      >
        <MenuItem value=''>Select</MenuItem>
        {options.map((option) => (
          <MenuItem key={option.id} value={option.id}>
            {option.name}
          </MenuItem>
        ))}
      </Select>
      {error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}

export default HighestQualificationField
