import React from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Box,
} from "@mui/material";

interface StatusSectionProps {
  formData: {
    status: string;
  };
  errors: {
    status?: string;
  };
  onChange: (e: { target: { name: string; value: string } }) => void;
  statusOptions: string[];
}

const StatusSection: React.FC<StatusSectionProps> = ({
  formData,
  errors,
  onChange,
  statusOptions,
}) => {
  return (
    <Box
      sx={{
        margin: "0px 0px 8px 0px",
        display: "flex",
        flexWrap: "wrap",
        width: { xs: "100%", sm: "48%", md: "48%" },
        mb: 0,
      }}
    >
      <FormControl
        size="small"
        sx={{
          width: "100%",
          "& .MuiInputBase-root": {
            height: "50px",
            display: "flex",
            
          },
          "& .MuiOutlinedInput-root": {
            borderRadius: "50px",
          },
        }}
        error={!!errors.status}
      >
        <InputLabel >Stage</InputLabel>
        <Select
          label="Stage"
          name="stage"

          value={formData.status}

          onChange={(e) =>
            onChange({
              target: {
                name: "status",
                value: e.target.value as string,
              },
            })
          }

        >
          <MenuItem value="" >Select</MenuItem>
          {statusOptions.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>
        {errors.status && <FormHelperText>{errors.status}</FormHelperText>}
      </FormControl>
    </Box>
  );
};

export default StatusSection;