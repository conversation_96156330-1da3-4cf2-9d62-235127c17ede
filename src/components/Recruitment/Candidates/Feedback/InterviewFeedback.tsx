import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
} from '@mui/material'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { connect } from 'react-redux'
import { addFeedbackCandidate } from 'actions'
import { RootState } from 'configureStore'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'

interface InterviewFeedbackProps {
  open: boolean
  handleClose: () => void
  candidateId: number
  refresh: boolean
  setRefresh: (value: boolean) => void
}

function InterviewFeedback(props: any) {
  const [grade, setGrade] = useState('')
  const [editorData, setEditorData] = useState('')
  const {
    candidateId,
    addFeedbackCandidate,
    handleClose,
    open,
    refresh,
    setRefresh,
  } = props
  const isSubmitDisabled = !editorData.replace(/<[^>]*>/g, '').trim() && !grade

  const handleAdd = () => {
    const feedbackData = {
      candidate_id: candidateId,
      feedback: editorData,
      feedback_id: '',
      grade: grade,
    }
    addFeedbackCandidate(feedbackData)
    setRefresh(!refresh)
    handleClose()
    setEditorData('')
    setGrade('')
  }

  const handleDialogClose = () => {
    setEditorData('')
    setGrade('')
    handleClose()
  }

  return (
    <Dialog open={open} onClose={handleDialogClose} fullWidth maxWidth='md'>
      <DialogTitle
        sx={{
          fontWeight: 'bold',
          margin: 'auto',
          color: '#193C6D',
          fontSize: '24px',
          fontFamily: 'Montserrat-SemiBold',
        }}
      >
        Interview Feedback
      </DialogTitle>
      <DialogContent>
        <Box
          sx={{
            height: '90px',
            overflowY: 'auto',
            border: '1px solid #ccc',
            borderRadius: 1,
            padding: 1,
            mb: 2,
          }}
        >
          <CKEditor
            editor={ClassicEditor}
            data={editorData}
            onChange={(_event: unknown, editor: any) => {
              const data = editor.getData()
              setEditorData(data)
            }}
            config={{
              toolbar: [
                'undo',
                'redo',
                '|',
                'paragraph',
                '|',
                'bold',
                'italic',
                '|',
                'link',
                'insertTable',
                'blockQuote',
                '|',
                'bulletedList',
                'numberedList',
                '|',
                'outdent',
                'indent',
                'alignment',
              ],
            }}
          />
        </Box>
        <FormControl fullWidth sx={{ marginTop: 2 }}>
          <InputLabel sx={{ borderRadius: '40px' }}>Select Grade</InputLabel>
          <Select
            value={grade}
            onChange={(e) => setGrade(e.target.value)}
            label='Select Grade'
            style={{ borderRadius: '40px', fontFamily: 'Montserrat-Medium' }}
          >
            <MenuItem value='A'>A (Awesome Candidate)</MenuItem>
            <MenuItem value='B'>B (Good Candidate)</MenuItem>
            <MenuItem value='C'>C (Average Candidate)</MenuItem>
            <MenuItem value='D'>D (Below Average Candidate)</MenuItem>
            <MenuItem value='E'>E (Reject this candidate)</MenuItem>
            <MenuItem value='F'>F (Utterly useless candidate)</MenuItem>
          </Select>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: 2, gap: 2 }}>
          <Button
            variant='contained'
            sx={{
              backgroundColor: '#E0E0E0',
              '&:hover': {
                backgroundColor: '#E0E0E0',
                color: 'black',
                boxShadow: '0 0 0 0.2',
              },
              color: 'black',
              borderRadius: '24px',
              padding: '6px 20px',
              marginBottom: '20px',
              width: '120px',
              fontSize: '20px',
              fontFamily: 'Montserrat-SemiBold',
            }}
            onClick={handleDialogClose}
          >
            Cancel
          </Button>
          <Button
            variant='contained'
            sx={{
              backgroundColor: '#1A3A6F',
              color: 'white',
              borderRadius: '24px',
              padding: '6px 20px',
              marginRight: '20px',
              marginBottom: '20px',
              width: '120px',
              fontSize: '20px',
              fontFamily: 'Montserrat-SemiBold',
            }}
            onClick={handleAdd}
            disabled={isSubmitDisabled}
          >
            Submit
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  )
}

const mapStateToProps = (state: RootState) => ({
  response: recruitmentEntity.getRecruitment(state).addFeedbackCandidate,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  addFeedbackCandidate: (data: any) =>
    dispatch(addFeedbackCandidate.request(data)),
})

export default connect(mapStateToProps, mapDispatchToProps)(InterviewFeedback)
