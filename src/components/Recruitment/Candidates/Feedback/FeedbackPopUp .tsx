import React from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Checkbox,
  FormControlLabel,
  Box,
  Typography,
  MenuItem,
  Select,
} from '@mui/material'
import { recruitmentEntity } from 'reducers'
import { Dispatch } from 'redux'
import { addFeedbackCandidate } from 'actions'
import { connect } from 'react-redux'
import { RootState } from 'configureStore'
import { InputBaseComponentProps } from '@mui/material'
import { useFeedbackForm } from './formikFeedbackForm'

interface FeedbackPopUpProps {
  open: boolean
  onClose: () => void
  candidateId: string
  setMarksFeeback: (feedback: string) => void
  setIsVisible: (value: boolean) => void
  refresh: boolean
  setRefresh: (value: boolean) => void
  addFeedbackCandidate: (data: any) => void
}

function FeedbackPopUp({
  open,
  onClose,
  candidateId,
  setMarksFeeback,
  setIsVisible,
  refresh,
  setRefresh,
  addFeedbackCandidate,
}: FeedbackPopUpProps) {

  const formik = useFeedbackForm({
    candidateId,
    setMarksFeeback,
    setIsVisible,
    refresh,
    setRefresh,
    addFeedbackCandidate,
    onClose,
  });

  const numberInputProps: InputBaseComponentProps = {
    inputMode: 'decimal',
    pattern: '[0-9]*[.]?[0-9]*',
    onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => {
      const allowedChars = /[0-9.]/
      if (!allowedChars.test(e.key)) {
        e.preventDefault()
      }
    },
  }

  return (
    <Dialog
      open={open}
      onClose={() => {
        formik.resetForm()
        onClose()
      }}
      maxWidth='sm'
      fullWidth
      sx={{ '& .MuiPaper-root': { borderRadius: '30px' ,height:"380px" } }}
    >
      <Box display='flex' justifyContent='space-between' alignItems='center' p={2}>
        <Typography
          variant='h6'
          sx={{
            color: '#193C6D',
            fontWeight: 'bold',
            margin: 'auto',
            fontSize: '28px',
          }}
        >
          Add Written Feedback
        </Typography>

      </Box>
      <form onSubmit={formik.handleSubmit}>
        <DialogContent>
          <Box display='flex' justifyContent='center' flexDirection='row' gap={2} height={"90px"}>
            <TextField
              label='Enter total marks'
              name='totalMarks'
              value={formik.values.totalMarks}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.totalMarks && Boolean(formik.errors.totalMarks)}
              helperText={formik.touched.totalMarks && formik.errors.totalMarks}
              inputProps={numberInputProps}
              sx={{ width: '250px', '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
            />

            <TextField
              label='Enter obtained marks'
              name='obtainedMarks'
              value={formik.values.obtainedMarks}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.obtainedMarks && Boolean(formik.errors.obtainedMarks)}
              helperText={formik.touched.obtainedMarks && formik.errors.obtainedMarks}
              inputProps={numberInputProps}
              sx={{ width: '250px', '& .MuiOutlinedInput-root': { borderRadius: '30px' } }}
            />
          </Box>
          <FormControlLabel
            control={
              <Checkbox
                name='otherChecked'
                checked={formik.values.otherChecked}
                onChange={(e) => formik.setFieldValue('otherChecked', e.target.checked)}
              />
            }
            label='Other'
            sx={{ mt: 2, height: "74.5px" }}
          />

          {formik.values.otherChecked && (
            <Select
              name='dropdownValue'
              value={formik.values.dropdownValue}
              onChange={formik.handleChange}
              displayEmpty
              fullWidth
              sx={{ mt: 2, width: '250px', borderRadius: '30px' }}
            >
              <MenuItem value='' disabled>
                Select an option
              </MenuItem>
              <MenuItem value='NA'>NA</MenuItem>
              <MenuItem value='CM'>CM</MenuItem>
            </Select>
          )}
        </DialogContent>
        <DialogActions>
          <Box display='flex' justifyContent='flex-end' mb={1.5} mr={1.5} gap={2}>
            <Button
              variant='contained'
              onClick={() => {
                formik.resetForm()
                onClose()
              }}
              sx={{
                backgroundColor: '#E0E0E0',
                color: 'black',
                borderRadius: '30px',
                padding: '6px 20px',
                width: '120px',
              }}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              variant='contained'
              disabled={!formik.isValid || !formik.dirty}
              sx={{
                backgroundColor: '#1A3A6F',
                color: 'white',
                borderRadius: '30px',
                padding: '6px 20px',
                width: '120px',
              }}
            >
              Add
            </Button>
          </Box>
        </DialogActions>
      </form>
    </Dialog>
  )
}

const mapStateToProps = (state: RootState) => ({
  response: recruitmentEntity.getRecruitment(state).addFeedbackCandidate,
})

const mapDispatchToProps = (dispatch: Dispatch) => ({
  addFeedbackCandidate: (data: any) => dispatch(addFeedbackCandidate.request(data)),
})

export default connect(mapStateToProps, mapDispatchToProps)(FeedbackPopUp)