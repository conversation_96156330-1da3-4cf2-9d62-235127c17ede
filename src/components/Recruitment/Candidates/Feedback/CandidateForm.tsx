import React, { useState, useCallback, useEffect } from 'react'
import { Typography, Box } from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import PersonalInfoSection from './PersonalInfoSection'
import EducationSection from './EducationSection'
import ProfessionalSection from './ProfessionalSection'
import DateField from './DateField'
import StatusSection from './StatusSection'
import TagsInput from './TagsInput'
import ResumeUpload from './ResumeUpload'
import FormButtons from './FormButtons'
import TagsAndResumeSection from './TagsAndResumeSection'

interface CandidateData {
  id: number
  name: string
  email: string
  phone: string
  experience: string
  jobRole: string
  blockemail?: string
  highestQualification?: string
  subject?: string
  status?: string
  dob?: string
  tags?: string[]
  resume?: File | null
  qualification: string
  round: string
}

interface Qualification {
  id: number
  qualification: string
}

interface CandidateFormProps {
  open: boolean
  handleClose: () => void
  handleSave: (data: any) => void
  positionOptions: Array<{ id: number; name: string }>
  experiencesOptions: Array<{ id: number; experience: string }>
  qualificationOptions?: Qualification[]
  tagsOptions: any
  initialData?: CandidateData
  editCandidate: (data: any) => void
  uploadResume: (file: File) => void
}

export default function CandidateForm({
  open,
  handleClose,
  handleSave,
  positionOptions = [],
  experiencesOptions = [],
  qualificationOptions = [],
  tagsOptions,
  initialData = {
    id: -1,
    name: '',
    email: '',
    phone: '',
    experience: '',
    jobRole: '',
    qualification: '',
    round: '',
    subject: '',
    dob: '',
  },
  editCandidate,
  uploadResume,
}: CandidateFormProps) {
  const [formData, setFormData] = useState({
    Name: initialData.name || '',
    email: initialData.email || '',
    blockemail: initialData.blockemail || '',
    contact: initialData.phone || '',
    highestQualification: initialData.qualification || '',
    subject: initialData.subject || '',
    position: initialData.jobRole || '',
    experience: initialData.experience || '',
    status: initialData.status || '',
    dob: initialData.dob ? dayjs(initialData.dob) : null,
    tags: initialData.tags?.join(', ') || '',
    resume: initialData.resume || null,
  })

  const [initialFormData, setInitialFormData] = useState<typeof formData | null>(null)
  const [tags, setTags] = useState<string[]>(initialData.tags || [])
  const [initialTags, setInitialTags] = useState<string[]>(initialData.tags || [])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isFormValid, setIsFormValid] = useState(false)

  const qualificationList = qualificationOptions.map((qual) => ({
    id: qual.id,
    name: qual.qualification,
  }))

  const subjectOptions = ['Art', 'Commerce', 'Diploma', 'PCM', 'PCB']
  const statusOptions = [
    'Pending Final Decision',
    'Pending Final Interview',
    'Selected',
    'Selected and Did NOT Join',
    'Selected and Joined',
    'Shortlisted',
    'Video Round: First Interview',
    'Video Round: Second Interview',
    'Campus Joining Update',
    'Campus Joining Update',
    'coding round 1',
    'Connect Later',
    ' Did Not Show Up (Coding Round)',
    'Did Not Show Up (Final Call for First Interview)',
    'Did Not Show Up (Final Call for Written)',
    'Did Not Show Up (Final Interview)',
    'Did Not Show Up (First Interview)',
    'Did Not Show Up (Skype Interview)',
    'Did Not Show Up (Written)',
    'Document Requested',
    'Documents Received',
    'Final Interview',
    'First Interview',
    'General',
    'Joined and Left',
    ' Offer Letter Accepted',
    'Offer Letter Sent',
    'On Hold for Now',
    'Pending Final Decision',
  ]
  const tagOptions = tagsOptions[0]?.map((option: any) => option.name)
  const blockEmailOptions = ['Yes', 'No']
  const experienceOptions = experiencesOptions.map((option) => option.experience)

  useEffect(() => {
    if (initialData) {
      const newFormData = {
        Name: initialData.name || '',
        email: initialData.email || '',
        blockemail: initialData.blockemail || '',
        contact: initialData.phone || '',
        highestQualification: initialData.qualification || '',
        subject: initialData.subject || '',
        position: initialData.jobRole || '',
        experience: initialData.experience || '',
        status: initialData.status || '',
        dob: initialData.dob ? dayjs(initialData.dob) : null,
        tags: initialData.tags?.join(', ') || '',
        resume: initialData.resume || null,
      }
      setFormData(newFormData)
      setInitialFormData(newFormData)
      setTags(initialData.tags || [])
      setInitialTags(initialData.tags || [])
    }
  }, [initialData])

  useEffect(() => {
    validateForm()
  }, [formData, tags])

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, Name: e.target.value })

    // Optional: clear previous error as user types
    setErrors((prev) => ({ ...prev, Name: '' }))
  }

  const handleChange = (e: { target: { name: string; value: string } }) => {
    const { name, value } = e.target;
  
    if (name === 'contact') {
      // Remove all non-digit characters (spaces, special chars, letters)
      if (value === ' ' &&  /[^a-zA-Z0-9]/.test(value)) {
        return; // block space or any special character
      }
      
      const digitsOnly = value.replace(/\D/g, '');
  
      // Prevent input beyond 10 digits
      if (digitsOnly.length > 10) return;
  
      setFormData((prev) => ({ ...prev, [name]: digitsOnly }));
       if(value.length<10)
        return
      if (errors[name]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
  
      return;
    }
  
    if (name === 'Name' && value === ' ') {
      return;
    }

  
    setFormData((prev) => ({ ...prev, [name]: value }));
  
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  
  const handleDateChange = (date: Dayjs | null) => {
    setFormData((prev) => ({ ...prev, dob: date }))
    if (errors.dob) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.dob
        return newErrors
      })
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    setFormData((prev) => ({ ...prev, resume: file }))
    if (errors.resume) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.resume
        return newErrors
      })
    }
  }

  const removeResume = () => {
    setFormData((prev) => ({ ...prev, resume: null }))
    if (errors.resume) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.resume
        return newErrors
      })
    }
  }

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}
    let isDirty = false

    if (!formData.Name.trim()) {
      newErrors.Name = 'Name is required'
    }

    if (initialFormData && formData.Name !== initialFormData.Name) {
      const nameRegex = /^[A-Za-z\s]+$/
      if (!nameRegex.test(formData.Name)) {
        newErrors.Name = 'Enter a valid name'
      }
    }

    if (initialFormData && formData.email !== initialFormData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email)) {
        newErrors.email = 'Enter a valid email address'
      }
    }

    if (initialFormData && formData.contact !== initialFormData.contact) {
      const phoneRegex = /^\d{10}$/; // Only digits, exactly 10
    
      if (!phoneRegex.test(formData.contact)) {
        newErrors.contact = 'Enter a valid 10-digit phone number (no spaces or special characters)';
      }
    }
    
    if (initialFormData && formData.dob !== initialFormData.dob) {
      if (formData.dob) {
        const eighteenYearsAgo = dayjs().subtract(18, 'year')
        if (dayjs(formData.dob).isAfter(eighteenYearsAgo)) {
          newErrors.dob = 'Candidate must be at least 18 years old'
        }
      }
    }

    if (initialFormData) {
      const formDirty = Object.keys(formData).some((key) => {
        const formKey = key as keyof typeof formData
        if (formKey === 'dob') {
          const current = formData.dob
          const initial = initialFormData.dob
          if (current === null && initial === null) return false
          if (current === null || initial === null) return true
          return !current.isSame(initial, 'day')
        }
        if (formKey === 'resume') return formData.resume !== initialFormData.resume
        return formData[formKey] !== initialFormData[formKey]
      })

      const tagsDirty = JSON.stringify(tags) !== JSON.stringify(initialTags)
      isDirty = formDirty || tagsDirty
    }

    setErrors(newErrors)
    setIsFormValid(isDirty && Object.keys(newErrors).length === 0)
  }, [formData, initialFormData, tags, initialTags])

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault()

    if (!isFormValid) return

    const positionId =
      positionOptions.find((pos) => pos.name === formData.position)?.id.toString() || ''
    const experienceId =
      experiencesOptions.find((exp) => exp.experience === formData.experience)?.id.toString() || ''

    const processedTags = tags.map((tag) => {
      const foundTag = tagsOptions[0]?.find((t: any) => t.name === tag)
      return foundTag ? foundTag.id.toString() : tag
    })

    const payload = {
      id: initialData.id,
      name: formData.Name,
      email: formData.email,
      phone_no: formData.contact,
      status: formData.status,
      qualification: formData.highestQualification,
      stage: '',
      position: positionId,
      experience: experienceId,
      dob: formData.dob ? formData.dob.format('MM-DD-YYYY') : '',
      tag: processedTags,
      subjects: formData.subject,
      delete_tag: [],
      block_mail: formData.blockemail === 'Yes' ? '1' : '0',
      resume: formData.resume ? 'resume_filename.pdf' : '',
    }

    if (formData.resume) {
      await uploadResume(formData.resume)
    }

    editCandidate(payload)
    handleSave(initialData.id)
    handleClose()
  }

  return (
    <>
      <Typography
        variant='h4'
        fontWeight='bold'
        color='#193c6d'
        sx={{ textAlign: 'center', mb: 3, fontFamily: 'Montserrat-SemiBold' }}
      >
        {`${formData.Name.trim() || 'Candidate'}'s Information`}
      </Typography>

      <Box
        component='form'
        onSubmit={handleSubmit}
        sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, maxWidth: '95%', margin: 'auto' }}
      >
        <PersonalInfoSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          blockEmailOptions={blockEmailOptions}
        />
        <EducationSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          qualificationOptions={qualificationList}
          subjectOptions={subjectOptions}
        />
        <ProfessionalSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          positionOptions={positionOptions.map((option) => option.name)}
          experienceOptions={experienceOptions}
        />
        <DateField
          label='Date of Birth'
          value={formData.dob}
          onChange={handleDateChange}
          error={errors.dob}
        />
        <StatusSection
          formData={formData}
          errors={errors}
          onChange={handleChange}
          statusOptions={statusOptions}
        />
        <TagsAndResumeSection
          tags={tags}
          onTagsChange={setTags}
          tagOptions={tagOptions}
          resume={formData.resume}
          onResumeUpload={handleFileUpload}
          onResumeRemove={removeResume}
          resumeError={errors.resume}
        />
        <FormButtons onGoBack={handleClose} isFormValid={isFormValid} onSubmit={handleSubmit} />
      </Box>
    </>
  )
}
