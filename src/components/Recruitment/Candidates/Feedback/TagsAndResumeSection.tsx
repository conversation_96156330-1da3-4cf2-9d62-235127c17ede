import React from 'react'
import { 
  Box, 
  Button, 
  IconButton, 
  FormHelperText, 
  Typo<PERSON>, 
  TextField,
  Chip
} from '@mui/material'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import CloseIcon from '@mui/icons-material/Close'
import Autocomplete from '@mui/material/Autocomplete'
import { styled } from '@mui/material/styles'

interface TagsAndResumeSectionProps {
  tags: string[]
  onTagsChange: (tags: string[]) => void
  tagOptions: string[]
  resume: File | null
  onResumeUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
  onResumeRemove: () => void
  resumeError?: string
}

const MAX_FILE_SIZE_MB = 5
const MAX_FILENAME_LENGTH = 15

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
})

const truncateFilename = (filename: string) => {
  return filename.length > MAX_FILENAME_LENGTH
    ? filename.substring(0, MAX_FILENAME_LENGTH) + '...'
    : filename
}

const TagsAndResumeSection: React.FC<TagsAndResumeSectionProps> = ({
  tags,
  onTagsChange,
  tagOptions,
  resume,
  onResumeUpload,
  onResumeRemove,
  resumeError
}) => {
  return (
    <Box sx={{ 
      width: '100%', 
      display: 'flex', 
      flexDirection: { xs: 'column', sm: 'row' },
      gap: 3,
      alignItems: 'flex-start'
    }}>
      {/* Tags Input Section - 50% width on desktop */}
      <Box sx={{ 
        width: { xs: '100%', sm: '50%' },
        minWidth: 0  // Prevents overflow
      }}>
        <Autocomplete
          fullWidth
          multiple
          freeSolo
          options={tagOptions}
          value={tags}
          onChange={(_, newValue) => onTagsChange(newValue)}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip 
                variant="outlined" 
                label={option} 
                {...getTagProps({ index })} 
              />
            ))
          }
          renderInput={(params) => (
            <TextField
            required={false}
              {...params}
              label="Enter a New Tag"
              variant="standard"
              size="small"
              sx={{
                margin: 0,
                width: "100%",
                "& .MuiInputBase-root": {
                  paddingLeft: "10px",
                  paddingRight: "8px",
                  width: "95%"
                },
                "& .MuiInputLabel-shrink": {
                  transform: 'translate(12px, -5px) scale(0.75)'
                }
              }}
            />
          )}
        />
      </Box>

      {/* Upload Section - 50% width on desktop */}
      <Box sx={{ 
        width: { xs: '100%', sm: '50%' },
        minWidth: 0,  // Prevents overflow
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
      }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2 
        }}>
          <Button
            component='label'
            variant='contained'
            startIcon={<CloudUploadIcon />}
            sx={{
              backgroundColor: '#193c6d',
              color: 'white',
              padding: '10px 16px',
              fontSize: '14px',
              borderRadius: '24px',
              whiteSpace: 'nowrap',
              '&:hover': { backgroundColor: '#152a4e' },
            }}
          >
            Upload Resume
            <VisuallyHiddenInput 
              type='file' 
              onChange={onResumeUpload} 
              accept='.pdf' 
            />
          </Button>

          {resume && resume.size / (1024 * 1024) <= MAX_FILE_SIZE_MB && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'white',
                padding: '8px 12px',
                borderRadius: '8px',
              }}
            >
              <Typography variant='body2' sx={{ marginRight: '8px' }}>
                {truncateFilename(resume.name)}
              </Typography>
              <IconButton onClick={onResumeRemove} size="small">
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          )}
        </Box>
        
        {resumeError && (
          <FormHelperText error sx={{ ml: 2 }}>
            {resumeError}
          </FormHelperText>
        )}
      </Box>
    </Box>
  )
}

export default TagsAndResumeSection