import React, { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Box,
} from '@mui/material'
import DeleteIcon from '@mui/icons-material/Delete'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from '@mui/material'

interface Document {
  id: number
  name: string
}

interface Attachment {
  resume: string
  email: string
  id: number
}

interface CandidateData {
  id: number
  name: string
  email: string
  phone_no: string
  experience_id: number
  position_id: number
  updated_at: string
  status?: string
  attachments: Attachment[]
}

interface DocumentsTableProps {
  documentdata: CandidateData
  attachmentData: Attachment[]
  documents: Document[]
  onDelete: (id: number) => void
  deleteAttachment: (data: any) => void
}

interface DeleteConfirmationDialogProps {
  open: boolean
  handleClose: () => void
  handleConfirm: () => void
}

const DeleteConfirmationDialog = ({
  open,
  handleClose,
  handleConfirm,
}: DeleteConfirmationDialogProps) => {
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth='xs'
      fullWidth
      PaperProps={{
        sx: { borderRadius: '8px', width: '40%', fontFamily: 'Montserrat, sans-serif' },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: 'rgb(25, 60, 109)',
          color: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '1rem',
          marginBottom: '1rem',
          fontFamily: 'Montserrat, sans-serif',
          fontWeight: 'bold',
        }}
      >
        Confirm Deletion
      </DialogTitle>

      <DialogContent
        sx={{ padding: '20px', fontFamily: 'Montserrat, sans-serif', paddingBottom: '0rem' }}
      >
        <Typography sx={{ fontFamily: 'Montserrat, sans-serif' }}>
          Are you sure you want to delete this item? This action cannot be undone.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ padding: '20px', fontFamily: 'Montserrat, sans-serif' }}>
        <Button
          onClick={handleClose}
          variant='outlined'
          sx={{
            backgroundColor: 'rgb(25, 60, 109)',
            color: '#fff',
            fontWeight: 'bold',
            textTransform: 'none',
            fontSize: '13px',
            height: '35px',
            fontFamily: 'Montserrat, sans-serif',
            marginLeft: 'auto',
            border: '1px solid rgba(25, 60, 109)',
            borderRadius: '20px',
            padding: '5px 20px',
            '&:hover': { backgroundColor: 'rgb(25, 60, 109)' },
          }}
        >
          CANCEL
        </Button>
        <Button
          onClick={handleConfirm}
          variant='contained'
          sx={{
            backgroundColor: '#db3700',
            color: '#fff',
            fontWeight: 'bold',
            textTransform: 'none',
            fontSize: '13px',
            height: '35px',
            fontFamily: 'Montserrat, sans-serif',
            marginLeft: 'auto',
            border: '1px solid #db3700',
            borderRadius: '20px',
            padding: '5px 20px',
            '&:hover': { backgroundColor: '#db3700' },
          }}
        >
          DELETE
        </Button>
      </DialogActions>
    </Dialog>
  )
}

const DocumentsTable: React.FC<DocumentsTableProps> = ({
  documents,
  onDelete,
  documentdata,
  attachmentData,
  deleteAttachment,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<number | null>(null)

  const status = documentdata?.status || 'No Status Available'
  const email = documentdata?.email || 'No email Available'
  const date = documentdata?.updated_at || 'No Date Available'
  const resume = documents && documents.length > 0 ? documents[0]?.name : 'No Resume Found'

  const handleDeleteClick = (id: number | undefined) => {
    if (id !== undefined) {
      setItemToDelete(id)
      setDeleteDialogOpen(true)
    }
  }

  const handleConfirmDelete = () => {
    if (itemToDelete !== null) {
      deleteAttachment({ email, resume })
    }
    setDeleteDialogOpen(false)
    setItemToDelete(null)
  }

  const handleCloseDialog = () => {
    setDeleteDialogOpen(false)
    setItemToDelete(null)
  }

  return (
    <>
      <Box sx={{ display: 'flex', gap: 2, marginTop: 4 }}>
        <TableContainer
          sx={{ borderRadius: 1, marginTop: 0, width: '48%', display: 'inline-block' }}
        >
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: '#193C6D', color: 'white' }}>
                <TableCell sx={{ color: 'white', pl: '5%', fontWeight: 'bold' }}>
                  Documents
                </TableCell>
                <TableCell
                  sx={{ color: 'white', textAlign: 'right', pr: '25%', fontWeight: 'bold' }}
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                {documents && documents.length > 0 ? (
                  <>
                    <TableCell sx={{ pl: '5%', fontFamily: 'Montserrat-Medium' }}>
                      {resume}
                    </TableCell>
                    <TableCell sx={{ color: 'white', textAlign: 'right', pr: '25%', fontWeight: 'bold' }}>
                      <IconButton
                        color='error'
                        onClick={() => handleDeleteClick(documents[0]?.id)}
                        sx={{ '&:hover': { bgcolor: '#ffe6e6' } }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </>
                ) : (
                  <TableCell
                    colSpan={2}
                    sx={{
                      pl: '5%',
                      py: '26px',
                      fontFamily: 'Montserrat-Medium',
                      textAlign: 'center',
                    }}
                  >
                    No Resume Uploaded
                  </TableCell>
                )}
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        <TableContainer sx={{ borderRadius: 1, flex: 1, height: '150px' }}>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: '#193C6D', color: 'white' }}>
                <TableCell sx={{ color: 'white', pl: '5%', fontWeight: 'bold' }}>Status</TableCell>
                <TableCell
                  sx={{ color: 'white', textAlign: 'right', pr: '22%', fontWeight: 'bold' }}
                >
                  Date
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <TableCell sx={{ pl: '5%', fontFamily: 'Montserrat-Medium', height: '40px' }}>
                  {status}
                </TableCell>
                <TableCell sx={{ textAlign: 'right', pr: '10%', fontFamily: 'Montserrat-Medium' }}>
                  {date}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDialog}
        handleConfirm={handleConfirmDelete}
      />
    </>
  )
}

export default DocumentsTable
