import React, { useState } from 'react'
import { Box, Typography, Avatar, IconButton } from '@mui/material'
import DeleteIcon from '@mui/icons-material/Delete'
import DeleteConfirmationDialog from '../../Common/DeleteConfirmationDialog'

interface CandidateFeedback {
  id: number
  feedback: string
  candidate_id: number
  grade: string | null
  created_at: string
  assignment: string | null
  recruiter_name: string
  recruiter_image: string
  feedback_type: number
  note_type: string
}

interface MarksFeedbackShowProps {
  Data: CandidateFeedback[]
  handleDelete: (value: number) => void
}

const MarksFeedbackShow: React.FC<MarksFeedbackShowProps> = ({ Data, handleDelete }) => {
  const [openDialog, setOpenDialog] = useState(false)
  const [selectedId, setSelectedId] = useState<number | null>(null)

  const handleOpenDialog = (id: number) => {
    setSelectedId(id)
    setOpenDialog(true)
  }
  console.log(Data)
  const handleConfirmDelete = () => {
    if (selectedId !== null) {
      handleDelete(selectedId)
      setOpenDialog(false)
    }
  }

  if (Data.length === 0) {
    return (
      <Typography sx={{ textAlign: 'center', color: 'gray' }}>No Feedback Available</Typography>
    )
  }

  return (
    <Box sx={{ width: '97%', margin: '20px' }}>
      {Data.map((item) => {
        const formattedDate = new Date(item.created_at).toLocaleString('en-US', {
          weekday: 'short',
          year: 'numeric',
          month: 'short',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          timeZoneName: 'short',
        })

        const isSpecialFeedback = item.feedback === 'na' || item.feedback === 'cm'

        return (
          <Box
            key={item.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '10px',
              borderRadius: '10px',
              border: '1px solid #ddd',
              boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
              backgroundColor: '#fff',
              marginBottom: '10px',
              width: '100%',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                src={item.recruiter_image ? item.recruiter_image : 'https://via.placeholder.com/40'}
                alt={item.recruiter_name}
                sx={{ width: 40, height: 40, marginRight: '10px' }}
              />
              <Box>
                <Typography sx={{ fontWeight: 'bold', fontSize: '14px' }}>
                  {item.recruiter_name}
                </Typography>
                <Typography sx={{ fontSize: '12px', color: 'gray' }}>{formattedDate}</Typography>

                <Typography sx={{ fontSize: '14px', marginTop: '4px', color: '#555' }}>
                  {isSpecialFeedback ? item.note_type : item.feedback.replace(/<\/?p>/g, '')}
                </Typography>

                {!isSpecialFeedback && item.grade && (
                  <Typography sx={{ fontSize: '14px', marginTop: '4px', color: '#555' }}>
                    Grade: {item.grade}
                  </Typography>
                )}
              </Box>
            </Box>
            <IconButton
              sx={{ color: 'red', marginRight: '10px' }}
              onClick={() => handleOpenDialog(item.id)}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        )
      })}

      <DeleteConfirmationDialog
        open={openDialog}
        handleClose={() => setOpenDialog(false)}
        handleConfirm={handleConfirmDelete}
        message='Are you sure you want to delete this feedback? This action cannot be undone.'
      />
    </Box>
  )
}

export default MarksFeedbackShow
