import { useEffect } from "react";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";

export interface FormData {
  id?: number;
  name: string;
  description: string;
  round_name: string;
  subject: string;
  content: string;
  id_round: number | null;
}
export interface RowData {
  id: number;
  name: string;
  subject: string;
  content: string;
  description: string;
  id_round: number;
  round_name: string;
}

export const validationSchema = Yup.object().shape({
  name: Yup.string()
    .trim()
    .required("Name is required")
    .min(3, "Name must be at least 3 characters")
    .max(50, "Name cannot exceed 50 characters"),

  round_name: Yup.string()
    .trim()
    .required("Round is required"),

  subject: Yup.string()
    .trim()
    .required("Subject is required")
    .min(3, "Subject must be at least 3 characters")
    .max(100, "Subject cannot exceed 100 characters"),

  description: Yup.string()
    .trim()
    .required("Description is required")
    .min(5, "Description must be at least 5 characters")
    .max(500, "Description cannot exceed 500 characters"),

  content: Yup.string()
    .trim()
    .required("Content is required")
    .min(10, "Content must be at least 10 characters")
    .max(5000, "Content cannot exceed 5000 characters"),
});

export const useFetchRoundsEffect = (fetchRoundsForTemplate: () => void) => {
  useEffect(() => {
    fetchRoundsForTemplate();
  }, [fetchRoundsForTemplate]);
};

export const useTemplateStatusEffect = (
  isTemplateAdded: boolean,
  isTemplateEdited: boolean,
  resetTemplateAdded: () => void,
  resetTemplateEdited: () => void,
  navigate: (path: number) => void
) => {
  useEffect(() => {
    if (isTemplateAdded) {
      toast.success("Template created successfully");
      resetTemplateAdded();
      navigate(-1);
    }
    if (isTemplateEdited) {
      toast.success("Template updated successfully");
      resetTemplateEdited();
      navigate(-1);
    }
  }, [isTemplateAdded, isTemplateEdited, resetTemplateAdded, resetTemplateEdited, navigate]);
};

export const useTemplateFormFields = (
  roundOptions: any,
  addEmailTemplateData: (data: any) => void,
  editTemplateDetails: (data: any) => void
) => {
  const location = useLocation();
  const rowData = location.state?.Template as RowData | undefined;
  const roundArrayOptions =
    Array.isArray(roundOptions) && Array.isArray(roundOptions[0])
      ? roundOptions[0]
      : [];
  const options = roundArrayOptions.map((item: any) => item.round_name);
  const initialValues: FormData = {
    id: rowData ? rowData.id : -1,
    name: rowData ? rowData.name : "",
    description: rowData ? rowData.description : "",
    round_name: rowData ? rowData.round_name : "",
    subject: rowData ? rowData.subject : "",
    content: rowData ? rowData.content : "",
    id_round: rowData ? rowData.id_round : null,
  };
  const getRoundId = (roundName: string) => {
    const selected = roundArrayOptions.find((round: any) => round.round_name === roundName);
    return selected ? selected.id : null;
  };
  const onSubmit = (values: FormData) => {
    const finalValues = {
      ...values,
      id_round: getRoundId(values.round_name),
    };
    if (values.id !== -1) {
      editTemplateDetails(finalValues);
    } else {
      const { id, ...newData } = finalValues;
      addEmailTemplateData(newData);
    }
  };
  return { options, initialValues, onSubmit };
};
