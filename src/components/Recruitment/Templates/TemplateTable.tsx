import React from 'react'
import { IconButton, Tooltip } from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import { columnMappings, Template, TemplateTableProps } from './utils'
import DeleteConfirmationDialog from '../Common/DeleteConfirmationDialog'
import { useTemplateTableLogic } from './utils'
import GeneralizedTable from '../Common/GeneralizedTable'
import useOpenable from '../../../hooks/useOpenable'

const TemplateTable: React.FC<TemplateTableProps> = ({ data, deleteTemplate }) => {
  const {
    displayColumns,
    order,
    orderBy,
    handleDeleteClick,
    handleConfirmDelete,
    handleSort,
    sortedData,
    handleEditClick,
  } = useTemplateTableLogic(data, deleteTemplate)
  const { isOpen, onOpen, onClose } = useOpenable()
  const renderActions = (row: Template) => (
    <>
      <Tooltip title='Edit'>
        <IconButton
          onClick={() => handleEditClick(row)}
          sx={{ color: 'rgb(25,60,109)', width: '36px', height: '36px' }}
        >
          <EditIcon sx={{ width: '20px', height: '20px' }} />
        </IconButton>
      </Tooltip>
      <Tooltip title='Delete'>
        <IconButton
          onClick={() => {
            handleDeleteClick(row)
            onOpen()
          }}
          sx={{ color: '#db3700', width: '36px', height: '36px' }}
        >
          <DeleteIcon sx={{ width: '20px', height: '20px' }} />
        </IconButton>
      </Tooltip>
    </>
  )

  return (
    <>
      <GeneralizedTable
        columns={displayColumns}
        columnMappings={columnMappings}
        data={sortedData}
        order={order}
        orderBy={orderBy}
        onSort={handleSort}
        renderActions={renderActions}
      />
      <DeleteConfirmationDialog
        open={isOpen}
        handleClose={onClose}
        handleConfirm={() => {
          handleConfirmDelete()
          onClose()
        }}
      />
    </>
  )
}

export default TemplateTable
