import { connect } from 'react-redux'
import { RootState } from '../../../configureStore'
import { Dispatch } from 'redux'
import { deleteTemplate, getTemplates } from '../../../actions'
import { recruitmentEntity, recruitmentStateUI } from '../../../reducers'
import Templates from './TemplateList'

const mapDispatchToProps = (dispatch: Dispatch) => ({
  getTemplates: () => dispatch(getTemplates.request()),
  deleteTemplate: (data: { id: number }) => dispatch(deleteTemplate.request(data)),
  resetTemplateDeleted: () => dispatch(deleteTemplate.reset()),
})

const mapStateToProps = (state: RootState) => ({
  templateData: recruitmentEntity.getRecruitment(state).getAllTemplatesDetails,
  isGetTemplateData: recruitmentStateUI.getRecruitment(state).isGetAllTemplatesList,
  isTemplateDeleting: recruitmentStateUI.getRecruitment(state).isTemplateDeleting,
  isTemplateDeleted: recruitmentStateUI.getRecruitment(state).isTemplateDeleted,
})

const TemplateListTableDataMapped = connect(mapStateToProps, mapDispatchToProps)(Templates)

const TemplateList = () => (
  <>
    <TemplateListTableDataMapped />
  </>
)

export default TemplateList
