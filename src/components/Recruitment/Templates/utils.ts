import { useNavigate } from 'react-router-dom'
import { useState, ChangeEvent, useMemo, useCallback } from 'react'

export interface Template {
  id: number
  name: string
  subject: string
  content: string
  description: string
  created_at: string
  updated_at: string
  id_round: number
  round_name: string
}
export interface TemplateTableProps {
  data: Template[];
  deleteTemplate: (data: { id: number }) => void;
}

export const columnMappings: Record<string, string> = {
  name: 'Template Name',
  round_name: 'Round Name',
  description: 'Description',
}

export const processAPIResponse = (response: Template[] | { data?: Template[] }): Template[] => {
  let dataArray: Template[];
  if (Array.isArray(response)) {
    dataArray = response;
  } else {
    dataArray = response.data ?? [];
  }
  if (dataArray.length === 1 && Array.isArray(dataArray[0])) {
    dataArray = dataArray[0];
  }
  return dataArray;
};

export const useTemplateTableLogic = (
  data: Template[],
  deleteTemplate: (data: { id: number }) => void,
) => {
  type Order = 'asc' | 'desc'
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<keyof Template>('name')
  const [selectedRow, setSelectedRow] = useState<Template | null>(null)
  const displayColumns: (keyof Pick<Template, 'name' | 'round_name' | 'description'>)[] = [
    'name',
    'round_name',
    'description',
  ]
  const navigate = useNavigate()

  const handleDeleteClick = useCallback((row: Template) => {
    setSelectedRow(row)
  }, [])

  const handleConfirmDelete = () => {
    if (selectedRow) {
      deleteTemplate({ id: selectedRow.id })
    }
  }

  const handleSort = useCallback(
    (column: keyof Template) => {
      const isAsc = orderBy === column && order === 'asc'
      setOrder(isAsc ? 'desc' : 'asc')
      setOrderBy(column)
    },
    [order, orderBy],
  )

  const handleEditClick = useCallback(
    (row: Template) => {
      navigate('template', { state: { Template: row } })
    },
    [navigate],
  )

  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => {
        const aValue = (a[orderBy] ?? '').toString().trim().toLowerCase();
        const bValue = (b[orderBy] ?? '').toString().trim().toLowerCase();

        if (aValue < bValue) return order === 'asc' ? -1 : 1;
        if (aValue > bValue) return order === 'asc' ? 1 : -1;
        return 0;
    });
}, [data, order, orderBy]);

  return {
    displayColumns,
    order,
    orderBy,
    selectedRow,
    handleDeleteClick,
    handleConfirmDelete,
    handleSort,
    sortedData,
    handleEditClick,
  }
}

export interface TemplatesProps {
  getTemplates: () => void;
  resetTemplateDeleted: () => void;
  deleteTemplate: (data: { id: number }) => { type: string };
  templateData: Template[];
  isGetTemplateData: boolean;
  isTemplateDeleting: boolean;
  isTemplateDeleted: boolean;
}

export const useTemplatesLogic = (props: TemplatesProps) => {
  const { templateData } = props
  const [page, setPage] = useState<number>(1)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const rowsPerPage = 10
  const navigate = useNavigate()

  const processedData = useMemo(() => processAPIResponse(templateData), [templateData])

  const handleSearchChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    setPage(1)
  }, [])

  const clearSearch = useCallback(() => {
    setSearchQuery('')
    setPage(1)
  }, [])

  const handlePageChange = useCallback((event: ChangeEvent<unknown>, value: number) => {
    event.preventDefault()
    setPage(value)
  }, [])

  const filteredData = useMemo(() => {
    return processedData.filter(
      (item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.round_name.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  }, [searchQuery, processedData])

  const currentData = useMemo(() => {
    return filteredData.slice((page - 1) * rowsPerPage, page * rowsPerPage)
  }, [filteredData, page, rowsPerPage])

  return {
    page,
    searchQuery,
    rowsPerPage,
    handleSearchChange,
    clearSearch,
    handlePageChange,
    filteredData,
    currentData,
    navigate,
  }
}
