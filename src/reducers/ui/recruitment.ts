import { combineReducers } from 'redux'
import { actionTypes, fetchAllUserList } from '../../actions'
import { Actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import {
  DELETE_TEMPLATE,
  <PERSON>ETCH_SPAMING,
  FETCH_TEMPLATE_DETAILS,
  <PERSON>ETCH_COLLEGE_DETAILS,
  DELETE_COLLEGE,
  <PERSON>ETCH_CANDIDATE_BY_FILTERS,
  SENDMAIL_CANDIDATE_BY_IDS,
  FETCH_UNAPPROVED_CANDIDATE,
  ADD_DRIVE,
  EDIT_ORG,
  RESET,
  ADD_EMAIL_TEMPLATE_DATA,
  EDIT_TEMPLATE,
  FETCH_ROUNDS_FOR_TEMPLATE,
  DELETE_CANDIDATE_BY_ID,
  FETCH_JOINED_CANDIDATES,
  CANDIDATE_COUNT_BY_ROUND,
  FETCH_INTERVIEWER_WORK,
  DELETE_UNAPPROVED_CANDIDAT<PERSON>,
  FETCH_EXPECTED_JOINERS,
  AD<PERSON>_R<PERSON><PERSON>ECTED_BODY,
  GET_BLOCKED_BODY,
  <PERSON><PERSON><PERSON>_TAGS,
  FETCH_NEW_TAG,
  FETCH_EDIT_TAG,
  FETCH_DELETE_TAG,
  DELETE_ROUNDS,
  FETCH_ADD_ROUND,
  ADD_TPO,
  EDIT_TPO,
  EDIT_ROUNDS,
  FETCH_ROUND,
  FETCH_BLOCK_DOMAIN_DATA,
  FETCH_USER,
  FETCH_ORGANISATIONDETAILS,
  FETCH_DATEANDTIME,
  FETCH_ROUNDCANDIDATES,
  FETCH_TAGCANDIDATES,
  FETCH_POSITIONS,
  FETCH_EXPERIENCES,
  FETCH_BLOCKEDSUBJECTS,
  FETCH_ADDQUALIFICATION,
  FETCH_BATCHES,
  ADD_CANDIDATE,
  EDIT_CANDIDATE,
  ADD_CANDIDATE_FEEDBACK,
  DELETE_ATTACHMENT,
  DELETE_CANDIDATE_FEEDBACK,
  ADD_RESUME,
  DELETE_MULTIPLE_APPLICANTS,
} from '../../actions/actiontypes'

const { SUCCESS, REQUEST, FAILURE } = actionTypes

const ui = () => {
  const isGetAllTemplatesList = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_TEMPLATE_DETAILS[SUCCESS]:
      case FETCH_TEMPLATE_DETAILS[FAILURE]:
        return false
      case FETCH_TEMPLATE_DETAILS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isGetAllTemplatesListFailed = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_TEMPLATE_DETAILS[SUCCESS]:
      case FETCH_TEMPLATE_DETAILS[REQUEST]:
        return false
      case FETCH_TEMPLATE_DETAILS[FAILURE]:
        return true
      default:
        return state
    }
  }

  const isGetAllCollegesList = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_COLLEGE_DETAILS[SUCCESS]:
      case FETCH_COLLEGE_DETAILS[FAILURE]:
        return false
      case FETCH_COLLEGE_DETAILS[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isCandidatesByFiltersLoaded = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_CANDIDATE_BY_FILTERS[SUCCESS]:
        return true
      case FETCH_CANDIDATE_BY_FILTERS[FAILURE]:
        return true
      case FETCH_CANDIDATE_BY_FILTERS[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isCandidateMailSent = (state = [], action: Actions) => {
    switch (action.type) {
      case SENDMAIL_CANDIDATE_BY_IDS[SUCCESS]:
        return true
      case SENDMAIL_CANDIDATE_BY_IDS[FAILURE]:
      case SENDMAIL_CANDIDATE_BY_IDS[REQUEST]:
        return false
      default:
        return false
    }
  }

  const isTemplateDeleting = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_TEMPLATE[SUCCESS]:
      case DELETE_TEMPLATE[FAILURE]:
        return false
      case DELETE_TEMPLATE[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isTemplateDeleted = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_TEMPLATE[SUCCESS]:
        return true
      case DELETE_TEMPLATE[FAILURE]:
        return false
      case DELETE_TEMPLATE[REQUEST]:
        return false
      case DELETE_TEMPLATE[RESET]:
        return false
      default:
        return state
    }
  }

  const isCollegeDeleted = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_COLLEGE[SUCCESS]:
        return true
      case DELETE_COLLEGE[FAILURE]:
      case DELETE_COLLEGE[REQUEST]:
        return false
      case DELETE_COLLEGE[RESET]:
        return false

      default:
        return state
    }
  }

  const isCollegeDeleting = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_COLLEGE[SUCCESS]:
      case DELETE_COLLEGE[FAILURE]:
        return false
      case DELETE_COLLEGE[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isOrgEdited = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_ORG[SUCCESS]:
        return true
      case EDIT_ORG[FAILURE]:
      case EDIT_ORG[REQUEST]:
        return false
      case EDIT_ORG[RESET]:
        return false
      default:
        return state
    }
  }

  const isDriveAdded = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_DRIVE[SUCCESS]:
        return true
      case ADD_DRIVE[FAILURE]:
      case ADD_DRIVE[REQUEST]:
        return false
      case ADD_DRIVE[RESET]:
        return false
      default:
        return state
    }
  }

  const isOrgEditing = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_ORG[SUCCESS]:
      case EDIT_ORG[FAILURE]:
        return false
      case EDIT_ORG[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isDriveAdding = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_DRIVE[SUCCESS]:
      case ADD_DRIVE[FAILURE]:
        return false
      case ADD_DRIVE[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isCandidateDeleted = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_CANDIDATE_BY_ID[SUCCESS]:
        return true
      case DELETE_CANDIDATE_BY_ID[FAILURE]:
      case DELETE_CANDIDATE_BY_ID[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isInterviewerWorkLoaded = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_INTERVIEWER_WORK[SUCCESS]:
        return true
      case FETCH_INTERVIEWER_WORK[FAILURE]:
      case FETCH_INTERVIEWER_WORK[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isCandidateCountLoaded = (state = false, action: Actions) => {
    switch (action.type) {
      case CANDIDATE_COUNT_BY_ROUND[SUCCESS]:
        return true
      case CANDIDATE_COUNT_BY_ROUND[REQUEST]:
      case CANDIDATE_COUNT_BY_ROUND[FAILURE]:
        return false
      default:
        return state
    }
  }

  const isJoinedCandidatesLoaded = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_JOINED_CANDIDATES[SUCCESS]:
        return true
      case FETCH_JOINED_CANDIDATES[REQUEST]:
      case FETCH_JOINED_CANDIDATES[FAILURE]:
        return false
      default:
        return state
    }
  }

  const isFetchApplicants = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_UNAPPROVED_CANDIDATE[SUCCESS]:
        return false
      case FETCH_UNAPPROVED_CANDIDATE[FAILURE]:
        return false
      case FETCH_UNAPPROVED_CANDIDATE[REQUEST]:
        return true
      default:
        return state
    }
  }
  const isRoundDeleted = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_ROUNDS[SUCCESS]:
        return true
      case DELETE_ROUNDS[FAILURE]:
        return false
      case DELETE_ROUNDS[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isFetchApplicantsSpam = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_SPAMING[SUCCESS]:
        return false
      case FETCH_SPAMING[FAILURE]:
        return false
      case FETCH_SPAMING[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isTemplateAdded = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_EMAIL_TEMPLATE_DATA[SUCCESS]:
        return true
      case ADD_EMAIL_TEMPLATE_DATA[FAILURE]:
      case ADD_EMAIL_TEMPLATE_DATA[REQUEST]:
      case ADD_EMAIL_TEMPLATE_DATA[RESET]:
        return false
      default:
        return state
    }
  }

  const isTemplateAdding = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_EMAIL_TEMPLATE_DATA[SUCCESS]:
      case ADD_EMAIL_TEMPLATE_DATA[FAILURE]:
        return false
      case ADD_EMAIL_TEMPLATE_DATA[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isTemplateEdited = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_TEMPLATE[SUCCESS]:
        return true
      case EDIT_TEMPLATE[FAILURE]:
      case EDIT_TEMPLATE[REQUEST]:
      case EDIT_TEMPLATE[RESET]:
        return false
      default:
        return state
    }
  }

  const isTemplateEditing = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_TEMPLATE[SUCCESS]:
      case EDIT_TEMPLATE[FAILURE]:
        return false
      case EDIT_TEMPLATE[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isRoundsFetching = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_ROUNDS_FOR_TEMPLATE[SUCCESS]:
      case FETCH_ROUNDS_FOR_TEMPLATE[FAILURE]:
        return false
      case FETCH_ROUNDS_FOR_TEMPLATE[REQUEST]:
        return true
      default:
        return state
    }
  }
  const isExpectedJoinerData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_EXPECTED_JOINERS[SUCCESS]:
        return true
      case FETCH_SPAMING[FAILURE]:
      case FETCH_EXPECTED_JOINERS[REQUEST]:
        return true
      default:
        return state
    }
  }
  const getAddRound = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_ADD_ROUND[SUCCESS]:
        return true
      case FETCH_ADD_ROUND[REQUEST]:
      case FETCH_ADD_ROUND[FAILURE]:
        return false
      default:
        return state
    }
  }

  const isRejectedBodyAdded = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_REJECTED_BODY[SUCCESS]:
        return true
      case FETCH_SPAMING[FAILURE]:
      case ADD_REJECTED_BODY[REQUEST]:
        return true
      default:
        return state
    }
  }
  const AddTpo = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_TPO[SUCCESS]:
        return true
      case ADD_TPO[REQUEST]:
      case ADD_TPO[FAILURE]:
        return false
      default:
        return state
    }
  }

  const isBlockedBody = (state = false, action: Actions) => {
    switch (action.type) {
      case GET_BLOCKED_BODY[SUCCESS]:
        return true
      case FETCH_SPAMING[FAILURE]:
      case GET_BLOCKED_BODY[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isFetchTag = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_TAGS[SUCCESS]:
        return false
      case FETCH_TAGS[FAILURE]:
        return false
      case FETCH_TAGS[REQUEST]:
        return true
      default:
        return state
    }
  }
  const editTpo = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_TPO[SUCCESS]:
        return true
      case EDIT_TPO[REQUEST]:
      case ADD_TPO[FAILURE]:
        return false

      default:
        return state
    }
  }

  const addNewTag = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_NEW_TAG[SUCCESS]:
        return false
      case FETCH_NEW_TAG[FAILURE]:
        return false
      case FETCH_NEW_TAG[REQUEST]:
        return true
      default:
        return state
    }
  }

  const editNewTag = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_EDIT_TAG[SUCCESS]:
        return false
      case FETCH_EDIT_TAG[FAILURE]:
        return false
      case FETCH_EDIT_TAG[REQUEST]:
        return true
      default:
        return state
    }
  }
  const editRounds = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_ROUNDS[SUCCESS]:
        return true
      case EDIT_ROUNDS[REQUEST]:
      case ADD_TPO[FAILURE]:
        return false

      default:
        return state
    }
  }

  const deleteTag = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_DELETE_TAG[SUCCESS]:
        return false
      case FETCH_DELETE_TAG[FAILURE]:
        return false
      case FETCH_DELETE_TAG[REQUEST]:
        return true
      default:
        return state
    }
  }
  const getRoundLoadData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_ROUND[SUCCESS]:
        return true
      case EDIT_ROUNDS[REQUEST]:
        return false
      case FETCH_ROUND[REQUEST]:
        return false
      default:
        return state
    }
  }
  const isBlockDomains = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_BLOCK_DOMAIN_DATA[SUCCESS]:
        return true
      case FETCH_BLOCK_DOMAIN_DATA[FAILURE]:
        return false
      case FETCH_BLOCK_DOMAIN_DATA[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isgetAddQualificationData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_ADDQUALIFICATION[SUCCESS]:
        return true
      case FETCH_ADDQUALIFICATION[FAILURE]:
        return false
      case FETCH_ADDQUALIFICATION[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isAddResume = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_RESUME[SUCCESS]:
        return false
      case ADD_RESUME[FAILURE]:
        return false
      case ADD_RESUME[REQUEST]:
        return true
      default:
        return state
    }
  }

  const isgetBatchesData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_BATCHES[SUCCESS]:
        return true
      case FETCH_BATCHES[FAILURE]:
        return false
      case FETCH_BATCHES[REQUEST]:
        return false
      default:
        return state
    }
  }

  const getUser = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_USER[SUCCESS]:
        return true
      case FETCH_SPAMING[FAILURE]:
      case FETCH_USER[REQUEST]:
        return false
      default:
        return state
    }
  }

  const getOrganisationDetailsData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_ORGANISATIONDETAILS[SUCCESS]:
        return true
      case FETCH_SPAMING[FAILURE]:
      case FETCH_ORGANISATIONDETAILS[REQUEST]:
        return false
      default:
        return state
    }
  }

  const getDateandTimeData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_DATEANDTIME[SUCCESS]:
        return true
      case FETCH_SPAMING[FAILURE]:
      case FETCH_DATEANDTIME[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isCandidateRoundsData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_ROUNDCANDIDATES[SUCCESS]:
        return true
      case FETCH_ROUNDCANDIDATES[FAILURE]:
      case FETCH_ROUNDCANDIDATES[REQUEST]:
        return false
      default:
        return state
    }
  }
  const isCandidateTagsData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_TAGCANDIDATES[SUCCESS]:
        return true
      case FETCH_TAGCANDIDATES[FAILURE]:
        return false
      case FETCH_TAGCANDIDATES[REQUEST]:
        return false
      default:
        return state
    }
  }
  const isPositionData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_POSITIONS[SUCCESS]:
        return true
      case FETCH_POSITIONS[FAILURE]:
        return false
      case FETCH_POSITIONS[REQUEST]:
        return false
      default:
        return state
    }
  }
  const isExperienceData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_EXPERIENCES[SUCCESS]:
        return true
      case FETCH_EXPERIENCES[FAILURE]:
        return false
      case FETCH_EXPERIENCES[REQUEST]:
        return false
      default:
        return state
    }
  }
  const isBlockedSubjectData = (state = false, action: Actions) => {
    switch (action.type) {
      case FETCH_BLOCKEDSUBJECTS[SUCCESS]:
        return true
      case FETCH_BLOCKEDSUBJECTS[FAILURE]:
        return false
      case FETCH_BLOCKEDSUBJECTS[REQUEST]:
        return false
      default:
        return state
    }
  }

  const isCandidateAdded = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_CANDIDATE[SUCCESS]:
        return true
      case ADD_CANDIDATE[FAILURE]:
      case ADD_CANDIDATE[REQUEST]:
      case ADD_CANDIDATE[RESET]:
        return false
      default:
        return state
    }
  }

  const isCandidateEdited = (state = false, action: Actions) => {
    switch (action.type) {
      case EDIT_CANDIDATE[SUCCESS]:
        return true
      case EDIT_CANDIDATE[FAILURE]:
      case EDIT_CANDIDATE[REQUEST]:
      case EDIT_CANDIDATE[RESET]:
        return false
      default:
        return state
    }
  }

  const isFeedbackAdded = (state = false, action: Actions) => {
    switch (action.type) {
      case ADD_CANDIDATE_FEEDBACK[SUCCESS]:
        return true
      case ADD_CANDIDATE_FEEDBACK[FAILURE]:
      case ADD_CANDIDATE_FEEDBACK[REQUEST]:
      case ADD_CANDIDATE_FEEDBACK[RESET]:
        return false
      default:
        return state
    }
  }

  const isAttachmentDeleted = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_ATTACHMENT[SUCCESS]:
        return true
      case DELETE_ATTACHMENT[FAILURE]:
      case DELETE_ATTACHMENT[REQUEST]:
      case DELETE_ATTACHMENT[RESET]:
        return false
      default:
        return state
    }
  }

  const isFeedbackDeleted = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_CANDIDATE_FEEDBACK[SUCCESS]:
        return true
      case DELETE_CANDIDATE_FEEDBACK[REQUEST]:
        return false
      case DELETE_CANDIDATE_FEEDBACK[FAILURE]:
      case DELETE_CANDIDATE_FEEDBACK[RESET]:
        return false
      default:
        return state
    }
  }

  const getMultipleDeleteApplicants = (state = false, action: Actions) => {
    switch (action.type) {
      case DELETE_MULTIPLE_APPLICANTS[SUCCESS]:
        return true
      case DELETE_MULTIPLE_APPLICANTS[REQUEST]:
        return false
      case DELETE_MULTIPLE_APPLICANTS[REQUEST]:
        return false
      default:
        return state
    }
  }

  return combineReducers({
    isGetAllTemplatesList,
    isGetAllTemplatesListFailed,
    isTemplateDeleting,
    isGetAllCollegesList,
    isCollegeDeleted,
    isCandidatesByFiltersLoaded,
    isCandidateMailSent,
    isFetchApplicants,
    isFetchApplicantsSpam,
    isTemplateDeleted,
    isCollegeDeleting,
    isOrgEdited,
    isDriveAdded,
    isOrgEditing,
    isDriveAdding,
    isTemplateAdded,
    isTemplateAdding,
    isTemplateEdited,
    isTemplateEditing,
    isRoundsFetching,
    isCandidateDeleted,
    isInterviewerWorkLoaded,
    isJoinedCandidatesLoaded,
    isCandidateCountLoaded,
    isExpectedJoinerData,
    isRejectedBodyAdded,
    isBlockedBody,
    isFetchTag,
    addNewTag,
    editNewTag,
    deleteTag,
    isRoundDeleted,
    getAddRound,
    AddTpo,
    editTpo,
    editRounds,
    getRoundLoadData,
    isBlockDomains,
    getUser,
    getOrganisationDetailsData,
    getDateandTimeData,
    isCandidateRoundsData,
    isCandidateTagsData,
    isPositionData,
    isExperienceData,
    isBlockedSubjectData,
    isgetAddQualificationData,
    isgetBatchesData,
    isCandidateAdded,
    isCandidateEdited,
    isFeedbackAdded,
    isAttachmentDeleted,
    isFeedbackDeleted,
    isAddResume,
    getMultipleDeleteApplicants,
  })
}

export default ui

export const getRecruitment = (state: RootState) => state.ui.recruitment
