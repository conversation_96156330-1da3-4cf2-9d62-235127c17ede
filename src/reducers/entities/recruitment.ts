import { addFeedbackCandidate, addUploadResume, addUploadAssignment } from './../../actions/index'
import { combineReducers } from 'redux'
import {
  FETCH_ADDQUALIFICATION,
  FETCH_BATCHES,
  FETCH_EXPECTED_JOINERS,
  FETCH_CANDIDATEPOSITION,
  FETCH_FEEDBACK,
  FETCH_POSITIONS,
  FETCH_RECRUITERS,
  <PERSON>ETCH_ROUND,
  <PERSON>ET<PERSON>_ROUND_TYPE,
  FETCH_BLOCKEDSUBJECTS,
  <PERSON>ETCH_EXPERIENCES,
  FETCH_TAGS,
  <PERSON>ETCH_TPO_DETAILS,
  REQUEST,
  SUCCESS,
  FETCH_TEMPLATE_DETAILS,
  <PERSON><PERSON><PERSON>_TEMPLATE,
  <PERSON>ETCH_COLLEGE_DETAILS,
  DELETE_COLLEGE,
  FETCH_DATEANDTIME,
  <PERSON>ETCH_ORGANISATIONDETAILS,
  <PERSON><PERSON><PERSON>_USER,
  <PERSON><PERSON><PERSON>_<PERSON>AG_CANDIDATES,
  FET<PERSON>_BLOCK_DOMAIN_DATA,
  F<PERSON><PERSON>_EMAIL_TEMPLATES,
  <PERSON><PERSON>CH_STATIC_DATA,
  <PERSON><PERSON>CH_INTERVIEWER,
  CREATE_STATIC_DATA,
  <PERSON><PERSON><PERSON>_ROUNDCANDIDATES,
  FETCH_TAGCANDIDATES,
  ADD_REJECTED_SUBJECT,
  ADD_MULTIPLE_CANDIDATES,
  ADD_JOB_EXPERIENCE,
  FETCH_DATE_TIME_BY_ROUND,
  FETCH_TEMPLATE_BY_ROUND,
  FETCH_CANDIDATE_BY_FILTERS,
  DELETE_CANDIDATE_BY_ID,
  SENDMAIL_CANDIDATE_BY_IDS,
  VIEW_ATTACHMENTS_CANDIDATE,
  FETCH_VIDEO_URL,
  FETCH_INTERVIEWER_PANNEL,
  FETCH_APPLICANTS_QUALIFICATION,
  FETCH_UNAPPROVED_CANDIDATE,
  FETCH_SPAMING,
  DELETE_UNAPPROVED_CANDIDATE,
  FETCH_ROUNDS_BY_TYPE,
  DELETE_EXPECTED_JOINERS,
  ADD_DRIVE,
  EDIT_ORG,
  FETCH_INTERVIEWER_WORK,
  CANDIDATE_COUNT_BY_ROUND,
  FETCH_JOINED_CANDIDATES,
  FETCH_CANDIDATEBYID,
  FAILURE,
  ADD_MANAGEQUALIFICATION,
  ADD_MANAGEBATCHES,
  DELETE_QUALIFICATION,
  DELETE_BATCH,
  EDIT_BATCH,
  EDIT_QUALIFICATION,
  FETCH_REPRESENTATIVE_TYPE,
  ADD_ORG_DETAILS,
  ADD_USER_DETAILS,
  ADD_DATE_TIME_DETAILS,
  DELETE_ORG_DETAILS,
  DELETE_USER_DETAILS,
  DELETE_DATE_TIME_DETAILS,
  EDIT_ORG_DETAILS,
  EDIT_USER_DETAILS,
  ADD_EMAIL_TEMPLATE_DATA,
  FETCH_ROUNDS_FOR_TEMPLATE,
  EDIT_TEMPLATE,
  EDIT_CANDIDATE_INLINE,
  FETCH_INSTITUTE_DETAILS,
  ADD_JOB_POSITION,
  ADD_INST_DETAILS,
  EDIT_INST_DETAILS,
  SUB_INST_DETAILS,
  DEL_INST_DETAILS,
  ROUND_FEEDBACK_DROPDOWN,
  ADD_INTERVIEWER_DATA,
  FETCH_JOB_EXPERIENCE,
  EDIT_STATIC_DATA,
  EDIT_INTERVIEWER_DATA,
  DELETE_STATIC_DATA,
  DELETE_INTERVIEWER_DATA,
  ADD_EXPECTED_JOINERS,
  EDIT_EXPECTED_JOINERS,
  ADD_BLOCKED_BODY,
  DELETE_BLOCKED_BODY,
  GET_BLOCKED_BODY,
  ADD_REJECTED_BODY,
  DELETE_REJECTED_BODY,
  EDIT_REJECTED_BODY,
  FETCH_NEW_TAG,
  FETCH_EDIT_TAG,
  FETCH_DELETE_TAG,
  FETCH_ADD_ROUND,
  DELETE_ROUNDS,
  EDIT_ROUNDS,
  DELETE_TPO,
  EDIT_TPO,
  ADD_TPO,
  ORGANISATION_DETAILS_BY_TYPE,
  DELETE_BLOCK_DOMAIN_DATA,
  ADD_BLOCK_DOMAIN_DATA,
  EDIT_BLOCK_DOMAIN_DATA,
  ADD_CANDIDATE_FEEDBACK,
  DELETE_ATTACHMENT,
  DELETE_CANDIDATE_FEEDBACK,
  EDIT_CANDIDATE,
  FETCH_BATCH_DROPDOWN,
  EDIT_DATE_TIME,
  EDIT_REJECTED_SUBJECT,
  EDIT_JOB_EXPERIENCE,
  DELETE_JOB_EXPERIENCE,
  DELETE_REJECTED_SUBJECT,
  EDIT_POSITION_DETAILS,
  SUB_POSITION_DETAILS,
  DELETE_POSITION_DETAILS,
  ADD_FEEDBACK,
  DELETE_FEEDBACK,
  ADD_CANDIDATE,
  ADD_RESUME,
  EDIT_CANDIDATEFORM,
  VIEW_RECRUITER_CALLS,
  VIEW_CANDIDATE_FEEDBACK,
  FETCH_SEND_REMAINDER,
  FETCH_COOLING_OFF_PERIOD,
  ADD_UPLOAD_RESUME,
  ADD_UPLOAD_ASSIGNMENT,
  EDIT_FEEDBACK,
  SUB_EDIT_FEEDBACK,
  APPLICANTS_APPROVE,
  APPLICANTS_REJECT,
  DELETE_MULTIPLE_APPLICANTS,
  FETCH_APPLICANTS_RESUME,
  RESET,
} from '../../actions/actiontypes'
import { Actions } from '../../actions/Types'
import { RootState } from '../../configureStore'
import { editExpectedJoiners, getUserData } from '../../actions'

const entity = () => {
  const getRoundData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ROUND[SUCCESS]:
        return action.payload
      case FETCH_ROUND[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getPositionData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_POSITIONS[SUCCESS]:
        return action.payload
      case FETCH_POSITIONS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getTagData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_TAGS[SUCCESS]:
        return action.payload
      case FETCH_TAGS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getRecruiterData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_RECRUITERS[SUCCESS]:
        return action.payload
      case FETCH_RECRUITERS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getExperienceData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_EXPERIENCES[SUCCESS]:
        return action.payload[0]
      case FETCH_EXPERIENCES[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getAllTemplatesDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_TEMPLATE_DETAILS[SUCCESS]:
        return action.payload
      case FETCH_TEMPLATE_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getDateandTimeData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_DATEANDTIME[SUCCESS]:
        return action.payload
      case FETCH_DATEANDTIME[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getDateTimeByRound = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_DATE_TIME_BY_ROUND[SUCCESS]:
        return action.payload
      case FETCH_DATE_TIME_BY_ROUND[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getOrganisationDetailsData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ORGANISATIONDETAILS[SUCCESS]:
        return action.payload
      case FETCH_ORGANISATIONDETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getUser = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_USER[SUCCESS]:
        return action.payload[0]
      case FETCH_USER[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getTpoDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_TPO_DETAILS[SUCCESS]:
        return action.payload
      case FETCH_TPO_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getRepresentativeTypeData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_REPRESENTATIVE_TYPE[SUCCESS]:
        return action.payload
      case FETCH_REPRESENTATIVE_TYPE[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCandidatesByFilters = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_CANDIDATE_BY_FILTERS[SUCCESS]:
        return action.payload
      case FETCH_CANDIDATE_BY_FILTERS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getRoundType = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ROUND_TYPE[SUCCESS]:
        return action.payload
      case FETCH_ROUND_TYPE[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getTemplateByRound = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_TEMPLATE_BY_ROUND[SUCCESS]:
        return action.payload
      case FETCH_TEMPLATE_BY_ROUND[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getBatchesData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_BATCHES[SUCCESS]:
        return action.payload[0]
      case FETCH_BATCHES[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getEmailTemplates = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_EMAIL_TEMPLATES[SUCCESS]:
        return action.payload
      case FETCH_EMAIL_TEMPLATES[REQUEST]:
        return []
      default:
        return state
    }
  }

  const deleteCandidateById = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_CANDIDATE_BY_ID[SUCCESS]:
        return action.payload
      case DELETE_CANDIDATE_BY_ID[REQUEST]:
        return []
      default:
        return state
    }
  }

  const sendmailCandidateByIds = (state = [], action: Actions) => {
    switch (action.type) {
      case SENDMAIL_CANDIDATE_BY_IDS[SUCCESS]:
        return action.payload
      case SENDMAIL_CANDIDATE_BY_IDS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const viewAttachments = (state = [], action: Actions) => {
    switch (action.type) {
      case VIEW_ATTACHMENTS_CANDIDATE[SUCCESS]:
        return action.payload
      case VIEW_ATTACHMENTS_CANDIDATE[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getStaticData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_STATIC_DATA[SUCCESS]:
        return action.payload
      case FETCH_STATIC_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getVideoUrl = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_VIDEO_URL[SUCCESS]:
        return action.payload
      case FETCH_VIDEO_URL[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getAddQualificationData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ADDQUALIFICATION[SUCCESS]:
        return action.payload
      case FETCH_ADDQUALIFICATION[REQUEST]:
        return []
      default:
        return state
    }
  }

  const deleteTemplate = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_TEMPLATE[SUCCESS]:
        return action.payload
      case DELETE_TEMPLATE[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getAllCollegesDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_COLLEGE_DETAILS[SUCCESS]:
        return action.payload
      case FETCH_COLLEGE_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getBlockedSubjectData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_BLOCKEDSUBJECTS[SUCCESS]:
        return action.payload[0]
      case FETCH_BLOCKEDSUBJECTS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const CandidateRoundsData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ROUNDCANDIDATES[SUCCESS]:
        return action.payload[0]
      case FETCH_ROUNDCANDIDATES[REQUEST]:
        return []
      default:
        return state
    }
  }
  const CandidateTagsData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_TAGCANDIDATES[SUCCESS]:
        return action.payload[0]
      case FETCH_TAGCANDIDATES[REQUEST]:
        return []
      default:
        return state
    }
  }
  const deleteCollege = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_COLLEGE[SUCCESS]:
        return action.payload
      case DELETE_COLLEGE[REQUEST]:
      default:
        return state
    }
  }

  const getExpectedJoinerData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_EXPECTED_JOINERS[SUCCESS]:
        return action.payload[0]
      case FETCH_EXPECTED_JOINERS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getAddExpectedJoinerData = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_EXPECTED_JOINERS[SUCCESS]:
        return action.payload[0]
      case ADD_EXPECTED_JOINERS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getFeedbackData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_FEEDBACK[SUCCESS]:
        return action.payload[0]
      case FETCH_FEEDBACK[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getInterviewer = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_INTERVIEWER[SUCCESS]:
        return action.payload
      case FETCH_INTERVIEWER[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getCandidatePositionData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_CANDIDATEPOSITION[SUCCESS]:
        return action.payload[0]
      case FETCH_CANDIDATEPOSITION[REQUEST]:
      default:
        return state
    }
  }

  const getRoundsByTypeData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ROUNDS_BY_TYPE[SUCCESS]:
        return action.payload[0]
      case FETCH_ROUNDS_BY_TYPE[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getTagCandidatesData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_TAG_CANDIDATES[SUCCESS]:
        return action.payload
      case FETCH_TAG_CANDIDATES[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getAllQualification = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_APPLICANTS_QUALIFICATION[SUCCESS]:
        return action.payload
      case FETCH_APPLICANTS_QUALIFICATION[REQUEST]:
        return []

      default:
        return state
    }
  }

  const getBlockDomainsData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_BLOCK_DOMAIN_DATA[SUCCESS]:
        return action.payload
      case FETCH_BLOCK_DOMAIN_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getAllUnapprovedCandidate = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_UNAPPROVED_CANDIDATE[SUCCESS]:
        return action.payload
      case FETCH_UNAPPROVED_CANDIDATE[REQUEST]:
        return []
      default:
        return state
    }
  }
  const postStaticData = (state = [], action: Actions) => {
    switch (action.type) {
      case CREATE_STATIC_DATA[SUCCESS]:
        return action.payload
      case CREATE_STATIC_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCandidateByID = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_CANDIDATEBYID[SUCCESS]:
        return action.payload
      case FETCH_CANDIDATEBYID[REQUEST]:
        return []
      case FETCH_CANDIDATEBYID[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const addMultipleCandidates = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_MULTIPLE_CANDIDATES[SUCCESS]:
        return {
          candidates: action.payload.candidateData || [],
          experience: action.payload.experience,
          position: action.payload.position,
          round: action.payload.round,
          tags: action.payload.tags || [],
        }
      case ADD_MULTIPLE_CANDIDATES[REQUEST]:
        return state
      default:
        return state
    }
  }
  const getInterviewerPannel = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_INTERVIEWER_PANNEL[SUCCESS]:
        return action.payload
      case FETCH_INTERVIEWER_PANNEL[REQUEST]:
        return []
      default:
        return state
    }
  }

  const addManageQualification = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_MANAGEQUALIFICATION[SUCCESS]:
        return action.payload
      case ADD_MANAGEQUALIFICATION[REQUEST]:
        return []

      default:
        return state
    }
  }

  const addManageBatches = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_MANAGEBATCHES[SUCCESS]:
        return action.payload
      case ADD_MANAGEBATCHES[REQUEST]:
        return []
      default:
        return state
    }
  }
  const addRejectedSubjects = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_REJECTED_SUBJECT[SUCCESS]:
        return action.payload
      case ADD_REJECTED_SUBJECT[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getAllSpaming = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_SPAMING[SUCCESS]:
        return action.payload
      case FETCH_SPAMING[REQUEST]:
        return []
        return state
      default:
        return state
    }
  }
  const addJobExperiences = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_JOB_EXPERIENCE[SUCCESS]:
        return action.payload
      case ADD_JOB_EXPERIENCE[REQUEST]:
        return []
        return state
      default:
        return state
    }
  }
  const editRejectedSubjects = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_REJECTED_SUBJECT[SUCCESS]:
        return state.map((subject: any) =>
          subject.id === action.payload.id ? { ...subject, ...action.payload.data } : subject,
        )
      case EDIT_REJECTED_SUBJECT[REQUEST]:
        return state
      default:
        return state
    }
  }
  const editJobExperiences = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_JOB_EXPERIENCE[SUCCESS]:
        return state.map((experience: any) =>
          experience.id === action.payload.id
            ? { ...experience, ...action.payload.data }
            : experience,
        )
      case EDIT_JOB_EXPERIENCE[REQUEST]:
        return state
      default:
        return state
    }
  }

  const deleteJobExperiences = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_JOB_EXPERIENCE[SUCCESS]:
        return state.filter((experience: any) => experience.id !== action.response.id)
      case DELETE_JOB_EXPERIENCE[REQUEST]:
        return state
      default:
        return state
    }
  }
  const deleteRejectedSubjects = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_REJECTED_SUBJECT[SUCCESS]:
        return state.filter((subject: any) => subject.id !== action.response.id)
      case DELETE_REJECTED_SUBJECT[REQUEST]:
        return state
      default:
        return state
    }
  }

  const deleteUnapproved = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_UNAPPROVED_CANDIDATE[SUCCESS]:
        return action.payload[0]
      case DELETE_UNAPPROVED_CANDIDATE[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getAddRound = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ADD_ROUND[SUCCESS]:
        return action.payload
      case FETCH_ADD_ROUND[REQUEST]:
        return []
      default:
        return state
    }
  }
  const fetchDeleteExpectedJoiners = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_EXPECTED_JOINERS[SUCCESS]:
        return state.filter((o: any) => o.id !== action.response.id)
      case DELETE_EXPECTED_JOINERS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const postDriveDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_DRIVE[SUCCESS]:
        return action.payload
      case ADD_DRIVE[REQUEST]:
        return []
      default:
        return state
    }
  }

  const postEditOrgDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_ORG[SUCCESS]:
        return action.payload
      case EDIT_ORG[REQUEST]:
        return []
      default:
        return state
    }
  }

  const deleteRounds = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_ROUNDS[SUCCESS]:
        return state.filter((experience: any) => experience.id !== action.response.id)
      case DELETE_ROUNDS[REQUEST]:
        return state

      default:
        return state
    }
  }

  const fetchInterviewerWork = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_INTERVIEWER_WORK[SUCCESS]:
        return action.payload
      case FETCH_INTERVIEWER_WORK[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getInterviewerData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_INTERVIEWER[SUCCESS]:
        return action.payload[0]
      case FETCH_INTERVIEWER[REQUEST]:
        return []
      default:
        return state
    }
  }
  const deleteTpo = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_TPO[SUCCESS]:
        return state.filter((experience: any) => experience.id !== action.response.id)
      case DELETE_TPO[REQUEST]:
        return state
      default:
        return state
    }
  }

  const fetchCandidateCount = (state = [], action: Actions) => {
    switch (action.type) {
      case CANDIDATE_COUNT_BY_ROUND[SUCCESS]:
        return action.payload
      case CANDIDATE_COUNT_BY_ROUND[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getAddOrganisationDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_ORG_DETAILS[SUCCESS]:
        return action.payload
      case ADD_ORG_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const editRounds = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_ROUNDS[SUCCESS]:
        return state.filter((experience: any) => experience.id !== action.response.id)
      case EDIT_ROUNDS[REQUEST]:
        return state

      default:
        return state
    }
  }

  const fetchJoinedCandidates = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_JOINED_CANDIDATES[SUCCESS]:
        return action.payload
      case FETCH_JOINED_CANDIDATES[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getAddUserDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_USER_DETAILS[SUCCESS]:
        return action.payload
      case ADD_USER_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const editTpo = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_TPO[SUCCESS]:
        return state.filter((experience: any) => experience.id !== action.response.id)
      case EDIT_TPO[REQUEST]:
        return state

      default:
        return state
    }
  }
  const deleteQualification = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_QUALIFICATION[SUCCESS]:
        return state.filter((q: any) => q.id !== action.response.id)
      case DELETE_QUALIFICATION[REQUEST]:
        return state
      default:
        return state
    }
  }

  const AddTpo = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_TPO[SUCCESS]:
        return state.filter((experience: any) => experience.id !== action.response.id)
      case ADD_TPO[REQUEST]:
        return state
      default:
        return state
    }
  }

  const deleteBatch = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_BATCH[SUCCESS]:
        return state.filter((b: any) => b.id !== action.response.id)
      case DELETE_BATCH[REQUEST]:
        return state
      default:
        return state
    }
  }

  const editBatch = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_BATCH[SUCCESS]:
        return state.map((batch: any) =>
          batch.id === action.payload.id ? { ...batch, ...action.payload.data } : batch,
        )
      case EDIT_BATCH[REQUEST]:
        return state
      default:
        return state
    }
  }

  const editQualification = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_QUALIFICATION[SUCCESS]:
        return state.map((qualification: any) =>
          qualification.id === action.payload.id
            ? { ...qualification, ...action.payload.data }
            : qualification,
        )
      case EDIT_QUALIFICATION[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getAddDateTimeDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_DATE_TIME_DETAILS[SUCCESS]:
        return action.payload
      case ADD_DATE_TIME_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getInstituteDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_INSTITUTE_DETAILS[SUCCESS]:
        return action.payload[0]
      case FETCH_INSTITUTE_DETAILS[REQUEST]:
        return []
      case FETCH_INSTITUTE_DETAILS[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }

  const postEmailTemplateData = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_EMAIL_TEMPLATE_DATA[SUCCESS]:
        return action.payload
      case ADD_EMAIL_TEMPLATE_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }
  const AddJobPosition = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_JOB_POSITION[SUCCESS]:
        return action.payload
      case ADD_JOB_POSITION[REQUEST]:
        return []
      case ADD_JOB_POSITION[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }

  const getDeleteOrgDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_ORG_DETAILS[SUCCESS]:
        return state.filter((o: any) => o.id !== action.response.id)
      case DELETE_ORG_DETAILS[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getDeleteUserDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_USER_DETAILS[SUCCESS]:
        return state.filter((o: any) => o.id !== action.response.id)
      case DELETE_USER_DETAILS[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getDeleteDateTimeDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_DATE_TIME_DETAILS[SUCCESS]:
        return state.filter((o: any) => o.id !== action.response.id)
      case DELETE_DATE_TIME_DETAILS[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getEditOrgDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_ORG_DETAILS[SUCCESS]:
        return action.payload
      case EDIT_ORG_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }

  const AddInstDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_INST_DETAILS[SUCCESS]:
        return action.payload
      case ADD_INST_DETAILS[REQUEST]:
        return []
      case ADD_INST_DETAILS[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }

  const getRoundsForTemplate = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ROUNDS_FOR_TEMPLATE[SUCCESS]:
        return action.payload
      case FETCH_ROUNDS_FOR_TEMPLATE[REQUEST]:
        return []
      default:
        return state
    }
  }

  const EditInstDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_INST_DETAILS[SUCCESS]:
        return action.payload
      case EDIT_INST_DETAILS[REQUEST]:
        return []
      case EDIT_INST_DETAILS[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const addBlockDomains = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_BLOCK_DOMAIN_DATA[SUCCESS]:
        return action.payload
      case ADD_BLOCK_DOMAIN_DATA[REQUEST]:
        return state

      default:
        return state
    }
  }

  const getEditUserDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_USER_DETAILS[SUCCESS]:
        return action.payload
      case EDIT_USER_DETAILS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const SubInstDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case SUB_INST_DETAILS[SUCCESS]:
        return action.payload
      case SUB_INST_DETAILS[REQUEST]:
        return []
      case SUB_INST_DETAILS[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const postEditTemplateDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_TEMPLATE[SUCCESS]:
        return action.payload
      case EDIT_TEMPLATE[REQUEST]:
        return []
      default:
        return state
    }
  }

  const DelInstDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case DEL_INST_DETAILS[SUCCESS]:
        return action.payload
      case DEL_INST_DETAILS[REQUEST]:
        return []
      case DEL_INST_DETAILS[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const editCandidateInline = (state = {}, action: Actions) => {
    switch (action.type) {
      case EDIT_CANDIDATE_INLINE[SUCCESS]:
        return action.payload
      case EDIT_CANDIDATE_INLINE[REQUEST]:
        return []
      case EDIT_CANDIDATE_INLINE[RESET]:
        return []
      default:
        return state
    }
  }
  const deleteBlockDomains = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_BLOCK_DOMAIN_DATA[SUCCESS]:
        return state.filter((domain: any) => domain.id !== action.response.id)
      case DELETE_BLOCK_DOMAIN_DATA[REQUEST]:
        return state
      default:
        return state
    }
  }
  const viewOrganisationDetailsByType = (state = [], action: Actions) => {
    switch (action.type) {
      case ORGANISATION_DETAILS_BY_TYPE[SUCCESS]:
        return action.payload
      case ORGANISATION_DETAILS_BY_TYPE[REQUEST]:
        return state

      default:
        return state
    }
  }

  const roundfeedbackDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case ROUND_FEEDBACK_DROPDOWN[SUCCESS]:
        return action.payload
      case ROUND_FEEDBACK_DROPDOWN[REQUEST]:
        return []
      case ROUND_FEEDBACK_DROPDOWN[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const addNewTag = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_NEW_TAG[SUCCESS]:
        return action.payload[0]
      case FETCH_NEW_TAG[REQUEST]:
        return []
      default:
        return state
    }
  }

  const editTag = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_EDIT_TAG[SUCCESS]:
        return action.payload[0]
      case FETCH_EDIT_TAG[REQUEST]:
        return []
      default:
        return state
    }
  }

  const editBlockDomains = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_BLOCK_DOMAIN_DATA[SUCCESS]:
        return state.filter((domain: any) => domain.id !== action.response.id)
      case EDIT_BLOCK_DOMAIN_DATA[REQUEST]:
        return state

      default:
        return state
    }
  }

  const deleteTag = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_DELETE_TAG[SUCCESS]:
        return action.payload[0]
      case FETCH_DELETE_TAG[REQUEST]:
        return []
      default:
        return state
    }
  }
  const getJobExperience = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_JOB_EXPERIENCE[SUCCESS]:
        return action.payload
      case FETCH_JOB_EXPERIENCE[REQUEST]:
        return []

      default:
        return state
    }
  }

  const postInterviewerData = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_INTERVIEWER_DATA[SUCCESS]:
        return action.payload

      case ADD_INTERVIEWER_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }
  const postEditStaticData = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_STATIC_DATA[SUCCESS]:
        return action.payload
      case EDIT_STATIC_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }
  const addFeedbackCandidate = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_CANDIDATE_FEEDBACK[SUCCESS]:
        return action.payload
      case ADD_CANDIDATE_FEEDBACK[REQUEST]:
        return state

      default:
        return state
    }
  }

  const postEditInterviewerData = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_INTERVIEWER_DATA[SUCCESS]:
        return action.payload
      case EDIT_INTERVIEWER_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }
  const postDeleteStaticData = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_STATIC_DATA[SUCCESS]:
        return action.payload
      case DELETE_STATIC_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }
  const addCandidate = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_CANDIDATE[SUCCESS]:
        return action.payload
      case ADD_CANDIDATE[FAILURE]:
        return action.payload
      case ADD_CANDIDATE[REQUEST]:
        return state
      default:
        return state
    }
  }

  const postDeleteInterviewerData = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_INTERVIEWER_DATA[SUCCESS]:
        return action.payload
      case DELETE_INTERVIEWER_DATA[REQUEST]:
        return []
      default:
        return state
    }
  }

  const editExpectedJoiners = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_EXPECTED_JOINERS[SUCCESS]:
        return state.filter((o: any) => o.id !== action.response.id)
      case EDIT_EXPECTED_JOINERS[REQUEST]:
        return state
      default:
        return state
    }
  }
  const deleteAttachment = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_ATTACHMENT[SUCCESS]:
        return state.filter((attach: any) => attach.id !== action.response.id)
      case DELETE_ATTACHMENT[REQUEST]:
        return state

      default:
        return state
    }
  }

  const addBlockedBody = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_BLOCKED_BODY[SUCCESS]:
        return state.filter((o: any) => o.id !== action.response.id)
      case ADD_BLOCKED_BODY[REQUEST]:
        return state
      default:
        return state
    }
  }
  const deleteCandidateFeedback = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_CANDIDATE_FEEDBACK[SUCCESS]:
        return state.filter((attach: any) => attach.id !== action.response.id)
      case DELETE_CANDIDATE_FEEDBACK[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getBlockedBody = (state = [], action: Actions) => {
    switch (action.type) {
      case GET_BLOCKED_BODY[SUCCESS]:
        return action.payload
      case GET_BLOCKED_BODY[REQUEST]:
        return state
      default:
        return state
    }
  }

  const addRejectedBody = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_REJECTED_BODY[SUCCESS]:
        return action.payload
      case ADD_REJECTED_BODY[REQUEST]:
        return state
      default:
        return state
    }
  }

  const deleteRejectedBody = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_REJECTED_BODY[SUCCESS]:
        return state.filter((sub: any) => sub.id !== action.response.id)
      case DELETE_REJECTED_BODY[REQUEST]:
        return state
      default:
        return state
    }
  }

  const editRejectedBody = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_REJECTED_BODY[SUCCESS]:
        return state.map((body: any) =>
          body.id === action.payload.id ? { ...body, ...action.payload.data } : body,
        )
      case EDIT_REJECTED_BODY[REQUEST]:
        return state
      default:
        return state
    }
  }
  const editCandidate = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_CANDIDATE[SUCCESS]:
        return action.payload
      case EDIT_CANDIDATE[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getBatchDropdownData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_BATCH_DROPDOWN[SUCCESS]:
        return action.payload
      case FETCH_BATCH_DROPDOWN[REQUEST]:
        return state
      default:
        return state
    }
  }
  const viewRecruitersCalls = (state = [], action: Actions) => {
    switch (action.type) {
      case VIEW_RECRUITER_CALLS[SUCCESS]:
        return action.payload
      case VIEW_RECRUITER_CALLS[REQUEST]:
        return []
      default:
        return state
    }
  }
  const editpositionDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_POSITION_DETAILS[SUCCESS]:
        return action.payload
      case EDIT_POSITION_DETAILS[REQUEST]:
        return []
      case EDIT_POSITION_DETAILS[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const submitpositionDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case SUB_POSITION_DETAILS[SUCCESS]:
        return action.payload
      case SUB_POSITION_DETAILS[REQUEST]:
        return []
      case SUB_POSITION_DETAILS[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const deletepositionDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_POSITION_DETAILS[SUCCESS]:
        return action.payload
      case DELETE_POSITION_DETAILS[REQUEST]:
        return []
      case DELETE_POSITION_DETAILS[FAILURE]:
        return action.payload.data
      case DELETE_POSITION_DETAILS[RESET]:
        return []
      default:
        return state
    }
  }
  const AddFeedbackDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_FEEDBACK[SUCCESS]:
        return action.payload
      case ADD_FEEDBACK[REQUEST]:
        return []
      case ADD_FEEDBACK[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }
  const DeleteFeedbackDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_FEEDBACK[SUCCESS]:
        return action.payload
      case DELETE_FEEDBACK[REQUEST]:
        return []
      case DELETE_FEEDBACK[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }

  const EditFeedbackDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_FEEDBACK[SUCCESS]:
        return action.payload
      case EDIT_FEEDBACK[REQUEST]:
        return []
      case EDIT_FEEDBACK[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }

  const SubEditFeedbackDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case SUB_EDIT_FEEDBACK[SUCCESS]:
        return action.payload
      case SUB_EDIT_FEEDBACK[REQUEST]:
        return []
      case SUB_EDIT_FEEDBACK[FAILURE]:
        return action.payload.data
      default:
        return state
    }
  }

  const getEditDateTimeDetails = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_DATE_TIME[SUCCESS]:
        return action.payload
      case EDIT_DATE_TIME[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCandidateRoundsData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_ROUNDCANDIDATES[SUCCESS]:
        return action.payload[0]
      case FETCH_ROUNDCANDIDATES[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCandidateTagsData = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_TAGCANDIDATES[SUCCESS]:
        return action.payload[0]
      case FETCH_TAGCANDIDATES[REQUEST]:
        return []
      default:
        return state
    }
  }

  const viewCandidateFeedback = (state = [], action: Actions) => {
    switch (action.type) {
      case VIEW_CANDIDATE_FEEDBACK[SUCCESS]:
        return action.payload
      case VIEW_CANDIDATE_FEEDBACK[REQUEST]:
        return state

      default:
        return state
    }
  }

  const getSendRemainder = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_SEND_REMAINDER[SUCCESS]:
        return action.payload
      case FETCH_SEND_REMAINDER[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getCoolingOffPeriod = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_COOLING_OFF_PERIOD[SUCCESS]:
        return action.payload
      case FETCH_COOLING_OFF_PERIOD[REQUEST]:
        return []
      default:
        return state
    }
  }

  const addUploadResume = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_UPLOAD_RESUME[SUCCESS]:
        return action.payload
      case ADD_UPLOAD_RESUME[REQUEST]:
        return state

      default:
        return state
    }
  }
  const addUploadAssignment = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_UPLOAD_ASSIGNMENT[SUCCESS]:
        return action.payload
      case ADD_UPLOAD_ASSIGNMENT[REQUEST]:
        return state

      default:
        return state
    }
  }
  const editCandidateForm = (state = [], action: Actions) => {
    switch (action.type) {
      case EDIT_CANDIDATEFORM[SUCCESS]:
        return action.payload
      case EDIT_CANDIDATEFORM[REQUEST]:
        return state
      default:
        return state
    }
  }

  const addResume = (state = [], action: Actions) => {
    switch (action.type) {
      case ADD_RESUME[SUCCESS]:
        return action.payload[0]
      case ADD_RESUME[REQUEST]:
        return []
      default:
        return state
    }
  }

  const getApproveApplicants = (state = [], action: Actions) => {
    switch (action.type) {
      case APPLICANTS_APPROVE[SUCCESS]:
        return action.payload
      case APPLICANTS_APPROVE[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getRejectedApplicants = (state = [], action: Actions) => {
    switch (action.type) {
      case APPLICANTS_REJECT[SUCCESS]:
        return action.payload
      case APPLICANTS_REJECT[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getMultipleDelete = (state = [], action: Actions) => {
    switch (action.type) {
      case DELETE_MULTIPLE_APPLICANTS[SUCCESS]:
        return action.payload
      case DELETE_MULTIPLE_APPLICANTS[REQUEST]:
        return state
      default:
        return state
    }
  }

  const getApplicantsResume = (state = [], action: Actions) => {
    switch (action.type) {
      case FETCH_APPLICANTS_RESUME[SUCCESS]:
        return action.payload
      case FETCH_APPLICANTS_RESUME[REQUEST]:
        return state
      default:
        return state
    }
  }

  return combineReducers({
    getRoundData,
    getPositionData,
    getRecruiterData,
    getTagData,
    getAllTemplatesDetails,
    deleteTemplate,
    getAllCollegesDetails,
    deleteCollege,
    getBatchesData,
    getAddQualificationData,
    getExpectedJoinerData,
    getFeedbackData,
    getCandidatePositionData,
    getTpoDetails,
    getRoundType,
    getAddRound,
    getBatchDropdownData,
    getDateandTimeData,
    getOrganisationDetailsData,
    getUser,
    getTagCandidatesData,
    getBlockDomainsData,
    getEmailTemplates,
    getStaticData,
    getInterviewer,
    postStaticData,
    getExperienceData,
    getBlockedSubjectData,
    getCandidateRoundsData,
    getCandidateTagsData,
    CandidateRoundsData,
    CandidateTagsData,
    addMultipleCandidates,
    addRejectedSubjects,
    addJobExperiences,
    getDateTimeByRound,
    getTemplateByRound,
    getCandidatesByFilters,
    deleteCandidateById,
    sendmailCandidateByIds,
    viewAttachments,
    getVideoUrl,
    getInterviewerPannel,
    getAllQualification,
    getAllUnapprovedCandidate,
    getAllSpaming,
    deleteUnapproved,
    getRoundsByTypeData,
    fetchDeleteExpectedJoiners,
    postDriveDetails,
    postEditOrgDetails,
    fetchInterviewerWork,
    fetchCandidateCount,
    fetchJoinedCandidates,
    getCandidateByID,
    addManageQualification,
    addManageBatches,
    deleteQualification,
    deleteBatch,
    editBatch,
    editQualification,
    getRepresentativeTypeData,
    getInterviewerData,
    getAddOrganisationDetails,
    getAddUserDetails,
    getAddDateTimeDetails,
    getDeleteOrgDetails,
    getDeleteUserDetails,
    getDeleteDateTimeDetails,
    getEditOrgDetails,
    getEditUserDetails,
    postEmailTemplateData,
    getRoundsForTemplate,
    postEditTemplateDetails,
    editCandidateInline,
    getInstituteDetails,
    AddJobPosition,
    AddInstDetails,
    EditInstDetails,
    SubInstDetails,
    DelInstDetails,
    roundfeedbackDetails,
    getJobExperience,
    postInterviewerData,
    postEditStaticData,
    postEditInterviewerData,
    postDeleteStaticData,
    postDeleteInterviewerData,
    getAddExpectedJoinerData,
    editExpectedJoiners,
    addBlockedBody,
    deleteRejectedBody,
    getBlockedBody,
    addRejectedBody,
    editRejectedBody,
    addNewTag,
    editTag,
    deleteTag,
    deleteRounds,
    editRounds,
    deleteTpo,
    editTpo,
    AddTpo,
    viewOrganisationDetailsByType,
    addBlockDomains,
    deleteBlockDomains,
    editBlockDomains,
    addFeedbackCandidate,
    deleteAttachment,
    deleteCandidateFeedback,
    editCandidate,
    getEditDateTimeDetails,
    editRejectedSubjects,
    editJobExperiences,
    deleteJobExperiences,
    deleteRejectedSubjects,
    editpositionDetails,
    submitpositionDetails,
    deletepositionDetails,
    AddFeedbackDetails,
    DeleteFeedbackDetails,
    addCandidate,
    editCandidateForm,
    addResume,
    viewRecruitersCalls,
    viewCandidateFeedback,
    getSendRemainder,
    getCoolingOffPeriod,
    addUploadResume,
    addUploadAssignment,
    EditFeedbackDetails,
    SubEditFeedbackDetails,
    getApproveApplicants,
    getRejectedApplicants,
    getMultipleDelete,
    getApplicantsResume,
  })
}
export default entity
export const getRecruitment = (state: RootState) => state.entities.recruitment
