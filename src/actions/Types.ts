import { Dayjs } from "dayjs"
import { GET_DAILY_SUMMARIES } from './actiontypes';

export type ACCTypes = {
  [key: string]: string
}

export type data = {
  data: object
}

export interface Actions {
  response: any
  type: string
  payload: any
  data?: []
}
export interface GetDailySummariesRequestAction {
  type: typeof GET_DAILY_SUMMARIES.REQUEST;
  data: {
    userId: string;
    tId: number;
  };
}
export interface DailySummariesPayload {
  userId: string;
  tId: number;
}

export type modulesType = {
  activeFlag: boolean
  code: string
  assigned: boolean
  clientId: null
  moduleId: string
  createdBy: string
  createdDate: null
  docId: string
  id: { timestamp: number; date: number }
  imageUrl: string
  isAssigned: boolean
  lastModifiedBy: string
  lastModifiedDate: null
  name: string
  subModuleDTOS: modulesType[]
  logoImagePath: string
  visible: boolean
  subModuleLength?: number
  link: string
}

export type getClientModulesRequestType = {
  id: string
}

export type getClientUserByIdRequestType = {
  userId: string
}

export type getRolesAndPermissionRequestType = {
  moduleName: string
}

type option = string|Dayjs|null

export type dataPayloadType = Record<string, option>

export interface SendMailCandidateParams {
  emailList: string[]
  id_round: string
  templateId: string
  mute: string
}